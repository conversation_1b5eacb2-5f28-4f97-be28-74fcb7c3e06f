import { firestore } from "./config-firebase.js";

import { extractQuestionSuggestions } from "../word/word-question-service.js";
//import { incremetGeneralStatistics } from "./statisticsService.js";
import {
  decrementGeneralStatistics,
  getStatisticsFieldValue,
  incremetGeneralStatistics,
} from "./statisticsService.js";
import { forEachAsync, mapAsyncSequential } from "../functions/async-foreach.js";
import { deleteSubcategory, deleteSubcategoryWithCatSub, getSubcategoryByName, updateSubcategoryQuestion } from "./subcategoryService.js";
import { FieldValue } from "firebase-admin/firestore";
import { deleteCategory } from "./categoryService.js";
import { saveToFileSystem } from "../functions/save.js";
import { mapAsync } from "../functions/async-map.js";

import { v4 as uuidv4 } from "uuid";

export const getQuestionById = async (id) => {
  const questionsRef = firestore.collection("Questions").doc(id);
  const questionsData = await questionsRef.get();
  if (!questionsData.exists) {
    return null
  }
  return { id: questionsData.id, ...questionsData.data() };
};
export const getQuestionsCountBySubcategory = async (subcategoryId) => {
  let ref = firestore
    .collection("Subcategories")
    .doc(subcategoryId)
    .collection("Questions");
  let snap = await ref.count().get();
  return snap.data();
};
export const createQuestion = async (category, subCategory, question) => {
  const questionsRef = firestore.collection("Questions");
  let addedQuestionId;
  try {
    const question_ = await questionsRef.add({
      categoryId: category.id,
      categoryName: category.name,
      subCategoryId: subCategory.id,
      subCategoryName: subCategory.name,
      text: question.Qst,
      year: parseInt(question["Annee Scolaire"].split("/")[0]),
      explanation: question.Commentaire,
      explanationImage: null,
      createdAt: new Date(),
    });
    console.log("question addedd successfully");
    addedQuestionId = question_.id;
  } catch (error) {
    console.log(error);
  }
  let foundQuestion;
  try {
    foundQuestion = await getQuestionById(addedQuestionId);
  } catch (error) {
    console.log(error);
  }

  try {
    const newQstInsideSubcategoryCollection = firestore
      .collection("Subcategories")
      .doc(subCategory.id)
      .collection("Questions")
      .doc(addedQuestionId);
    await newQstInsideSubcategoryCollection.set(foundQuestion);
  } catch (error) {
    console.log(error);
  }
};

export const deleteQuestion = async (qstId) => {
  const docRef = firestore.collection("Questions").doc(qstId);
  const doc = await docRef.get();
  const question = doc.data();
  try {
    await docRef.delete();
  } catch (error) {
    console.log(error.message);
  }
  const subcategoryRef = firestore
    .collection("Subcategories")
    .doc(question.subcategoryId)
    .collection("Questions")
    .doc(qstId);
  try {
    await subcategoryRef.delete();
  } catch (error) {
    console.log(error);
  }
};

export const deleteQeustionsBySubcategory = async (subcategoryName) => {
  const queryRef = firestore
    .collection("Questions")
    .where("subcategoryName", "==", subcategoryName);
  const query = await queryRef.get();
  let data = [];
  if (query.empty) {
    return [];
  }
  query.forEach((doc) => {
    data.push(doc.id);
  });
  await forEachAsync(data, deleteQuestion);
  console.log("all questions deleted");
  console.log(data);
};
export const updateQuestion = async (qstId, field, data) => {
  let docRef = firestore.collection("Questions").doc(qstId);

  try {
    await docRef.update({
      [field]: data,
    });
    console.log("question updated ===>", qstId);
  } catch (error) {
    console.log(error);
  }
};
export const updateQuestionLocation = async (qstId) => {
  let doc = await firestore.collection("Questions").doc(qstId).get();

  try {
    await updateQuestion(qstId, "location", "BATNA");
  } catch (error) {
    console.log(error);
  }
  try {
    await updateSubcategoryQuestion(
      doc.data().subcategoryId,
      qstId,
      "location",
      "BATNA"
    );
  } catch (error) {
    console.log(error);
  }
};

export const updateQuestionV2 = async (qstId, field, value) => {
  let doc = await firestore.collection("Questions").doc(qstId).get();

  try {
    await updateQuestion(qstId, field, value);
  } catch (error) {
    console.log(error);
  }
  try {
    await updateSubcategoryQuestion(
      doc.data().subcategoryId,
      qstId,
      field,
      value
    );
  } catch (error) {
    console.log(error);
  }
};
export const findQstByTextAndUpdate = async (
  text,
  subcategory,
  field,
  value
) => {
  let docs = await firestore
    .collection("Questions")
    .where("text", "==", text)
    .where("subcategoryName", "==", subcategory)
    .get();
  if (docs.empty) {
    console.log("question doesnr exist");
    return;
  }
  let questions = [];
  docs.forEach((doc) => {
    questions.push({ id: doc.id, ...doc.data() });
  });
  if (questions.length > 1) {
    console.log("many questions found");
    questions.forEach((question) => {

    });

    return;
  }
  let qstId = questions[0].id;
  try {
    await updateQuestion(qstId, field, value);
  } catch (error) {
    console.log(error);
  }
  let subcategoryId = questions[0].subcategoryId;
  try {
    await updateSubcategoryQuestion(subcategoryId, qstId, field, value);
  } catch (error) {
    console.log(error);
  }
};
export const updateQuestionCommentV3 = async (comment) => {
  let doc = await firestore.collection("Questions").doc(comment.id).get();

  try {
    await updateQuestion(comment.id, "explanation", comment.comment);
  } catch (error) {
    console.log(error);
  }
  try {
    await updateSubcategoryQuestion(
      doc.data().subcategoryId,
      comment.id,
      "explanation",
      comment.comment
    );
    console.log(comment.id, " is updated successfully");
  } catch (error) {
    console.log(error);
  }
};
export const updateQuestionComment = async (question) => {
  let docsRef = firestore.collection("Questions").where('text', "==", question.Qst)//.where("year", "==", question.year);
  let docsSnapshot = await docsRef.get()
  if (docsSnapshot.empty) {
    console.log("no remote question found for: ", question.Qst)
    return
  }
  let docs = []
  docsSnapshot.forEach(doc => {
    docs.push({ id: doc.id, ...doc.data() })
  })
  if (docs.length > 1) {
    console.log("many remote questions found !!!!!!!!", docs.length)
    let found = false
    forEachAsync(docs, async doc => {
      // extract suggestions texts and sort them

      if (!found) {
        const remoteSuggestions = doc.suggestions.map(s => s.text).slice(0, 3);
        const localSuggestions = [question.A, question.B, question.C]

        if (JSON.stringify(remoteSuggestions) === JSON.stringify(localSuggestions)) {
          found = true
          await updateQuestionV2(doc.id, "explanation", question.comment)
        }
      }

    })
    return
  }
  if (docs.length === 1) {
    let docId = docs[0].id
    //await updateQuestionV2(docId, "explanation", question.comment)
    await updateQuestionV2(docId, "year", 2024)
  }

};
export const getQuestionByCours = async (coursName) => {
  const queryRef = firestore
    .collection("Questions")
    .where("subcategoryName", "==", coursName);
  const query = await queryRef.get();
  if (query.empty) {
    return [];
  }
  const data = [];
  query.forEach((doc) => {
    data.push({ id: doc.id, ...doc.data() });
  });
  if (data.length === 1) return data[0];
  return data;
};

export const getQuestionByModule = async (moduleName) => {
  const queryRef = firestore
    .collection("Questions")
    .where("categoryName", "==", moduleName);
  const query = await queryRef.get();
  if (query.empty) {
    return [];
  }
  const data = [];
  query.forEach((doc) => {
    data.push({ id: doc.id, ...doc.data() });
  });
  return data;
};

export const getQuestionsByMultiplefield = async (catId, subCatId, text) => {
  const queryRef = firestore
    .collection("Questions")
    .where("categoryId", "==", catId)
    .where("subCategoryId", "==", subCatId)
    .where("text", "==", text);
  const query = await queryRef.get();
  if (query.empty) {
    return null;
  }
  const data = [];
  query.forEach((doc) => {
    data.push({ id: doc.id, ...doc.data() });
  });
  if (data.length === 1) return data[0];
  return data;
};

export const linkgetQuestionsIds = async () => {
  const questionsRef = firestore.collection("Questions");
  const questionsData = await questionsRef.get();
  let qstsIds = [];
  questionsData.forEach((qst) => {
    qstsIds.push(qst.id);
  });
  return qstsIds;
};
export const getQuestionsCount = async () => {
  try {
    const questionsRef = firestore.collection("Questions");
    const questionSize = await questionsRef.get();
    return questionSize.size;
  } catch (error) { }
};

export const getAllQuestions = async () => {
  const questionsRef = firestore.collection("Questions");
  const questions = await questionsRef.get();
  let data = [];
  questions.forEach((question) => {
    data.push({ id: question.id, ...question.data() });
  });
  //saveQuestions(data, "./question.json");

  return data;
};

export const getQuestionsIds = async () => {
  const questionsRef = firestore.collection("Questions");
  const questionsData = await questionsRef.get();
  let qstsIds = [];
  questionsData.forEach((qst) => {
    qstsIds.push({ qst });
  });
  return { id: questionsData.id, ...questionsData.data() };
};

export const getNewQuestionId = async (location) => {
  const questionsNumber = await getStatisticsFieldValue(location, "questions");
  return (questionsNumber + 1).toString();
};

export const createQuestionIfNotExists = async (
  location,
  category,
  subCategory,
  question
) => {
  let newQuestionId = await getNewQuestionId();
  const questionsRef = firestore.collection("Questions").doc(newQuestionId);

  try {
    await questionsRef.set({
      categoryId: category.id,
      categoryName: category.name,
      subcategoryId: subCategory.id,
      subcategoryName: subCategory.name,
      text: question.Qst,
      year: parseInt(question["Annee Scolaire"].split("/")[0]),
      explanation: question.Commentaire,
      explanationImage: null,
      createdAt: new Date(),
      suggestions: extractQuestionSuggestions(question),
    });
  } catch (error) {
    console.log(error);
  }
  let foundQuestion;
  try {
    foundQuestion = await getQuestionById(newQuestionId);
  } catch (error) {
    console.log(error);
  }

  try {
    const newQstInsideSubcategoryCollection = firestore
      .collection("Subcategories")
      .doc(subCategory.id)
      .collection("Questions")
      .doc(newQuestionId);
    await newQstInsideSubcategoryCollection.set(foundQuestion);
  } catch (error) {
    console.log(error);
  }

  try {
    await incremetGeneralStatistics("questions");
  } catch (error) {
    console.log(error);
  }
  //console.log("operation success");
};
const checkEmptyQuestionWithEmptySuggestions = (question) => {
  let responses = question.E ? ["A", "B", "C", "D", "E"] : ["A", "B", "C", "D"];
  if (responses.every((ele) => question[ele] === "") && question.Qst === "") {
    return true;
  }
  return false;
};
export const createQuestionIfNotExistsWithId = async (
  location,
  category,
  subCategory,
  question,
  id
) => {
  if (checkEmptyQuestionWithEmptySuggestions(question)) {
    console.log("empty question + empty suggestions");
    return;
  }
  let newQuestionId = id === undefined ? await getNewQuestionId("GENERAL") : id;
  const questionsRef = firestore.collection("Questions").doc(newQuestionId);

  try {
    await questionsRef.set({
      categoryId: category.id,
      categoryName: category.name,
      subcategoryId: subCategory.id,
      subcategoryName: subCategory.name,
      text: question.Qst,
      year: question.year,
      location,
      explanation: question.comment ?? "",
      explanationImage: null,
      createdAt: new Date(),
      suggestions: extractQuestionSuggestions(question),
    });
  } catch (error) {
    console.log(error);
  }
  let foundQuestion;
  try {
    foundQuestion = await getQuestionById(newQuestionId);
  } catch (error) {
    console.log(error);
  }

  try {
    const newQstInsideSubcategoryCollection = firestore
      .collection("Subcategories")
      .doc(subCategory.id)
      .collection("Questions")
      .doc(newQuestionId);
    await newQstInsideSubcategoryCollection.set(foundQuestion);
  } catch (error) {
    console.log(error);
  }

  if (id === undefined) {
    try {
      await incremetGeneralStatistics(location, "questions");
      await incremetGeneralStatistics("GENERAL", "questions");
      await incremetGeneralStatistics("GeneralStatistics", "questions");
    } catch (error) {
      console.log(error);
    }
  }
  //console.log("operation success");
};

export const getFreeIds = async () => {
  try {
    let freeIdsRef = firestore.collection("FreeIds").doc("free");
    let freeIds = await freeIdsRef.get();
    if (freeIds.exists) {
      return freeIds.data().free;
    }
    return [];
  } catch (error) {
    throw new Error("FreeeIds doesnot exist")
  }

};
export const extractIdFromFreeIds = async () => {
  let freeIds = await getFreeIds();
  if (freeIds.length === 0) return null
  let sortedIds = [...freeIds].sort((a, b) => a - b);
  let id = sortedIds.shift();
  await firestore.collection("FreeIds").doc("free").update({ free: sortedIds });
  return id;
};

export const updateFreeIds = async (ids) => {
  try {
    let freeIdsRef = firestore.collection("FreeIds").doc("free");
    let freeIds = await freeIdsRef.get();
    if (freeIds.exists) {
      let newFreeIds = [...new Set([...freeIds.data().free, ...ids])];
      await freeIdsRef.set({
        free: newFreeIds,
      });
    } else {
      await freeIdsRef.set({
        free: ids,
      });
    }
  } catch (error) {
    console.log(error);
  }
}
export const createQuestionWithIdV2 = async (
  location,
  category,
  subCategory,
  question
) => {
  if (checkEmptyQuestionWithEmptySuggestions(question)) {
    console.log("empty question + empty suggestions");
    return;
  }
  let freeIds = await getFreeIds();
  let newQuestionId =
    freeIds.length === 0
      ? await getNewQuestionId("GENERAL")
      : await extractIdFromFreeIds();
  const questionsRef = firestore.collection("Questions").doc(newQuestionId);

  try {
    await questionsRef.set({
      categoryId: category.id,
      categoryName: category.name,
      subcategoryId: subCategory.id,
      subcategoryName: subCategory.name,
      text: question.Qst,
      year: question.year,
      location,
      explanation: question.comment ?? "",
      explanationImage: null,
      createdAt: new Date(),
      suggestions: extractQuestionSuggestions(question),
    });
  } catch (error) {
    console.log("error while set");

    console.log(error);
  }
  let foundQuestion;
  try {
    foundQuestion = await getQuestionById(newQuestionId);
  } catch (error) {
    console.log("error while getQstById");

    console.log(error);
  }

  try {
    const newQstInsideSubcategoryCollection = firestore
      .collection("Subcategories")
      .doc(subCategory.id)
      .collection("Questions")
      .doc(newQuestionId);
    await newQstInsideSubcategoryCollection.set(foundQuestion);
  } catch (error) {
    console.log("error while update qst");

    console.log(error);
  }

  if (freeIds.length === 0) {
    try {
      await incremetGeneralStatistics(location, "questions");
      await incremetGeneralStatistics("GENERAL", "questions");
    } catch (error) {
      console.log("error while increment");
      console.log(error);
    }
  }
  //console.log("operation success");
};

export const createQuestionWithIdV21 = async (
  question,
  location,
  category,
  subCategory
) => {
  //console.log(question.Qst);

  if (checkEmptyQuestionWithEmptySuggestions(question)) {
    console.log("empty question + empty suggestions");
    return;
  }
  let freeIds = await getFreeIds();
  let newQuestionId =
    freeIds.length === 0
      ? await getNewQuestionId("GENERAL")
      : await extractIdFromFreeIds();
  const questionsRef = firestore.collection("Questions").doc(newQuestionId);

  try {
    await questionsRef.set({
      categoryId: category.id,
      categoryName: category.name,
      subcategoryId: subCategory.id,
      subcategoryName: subCategory.name,
      text: question.Qst,
      year: question.year,
      location,
      ...(question.type && { type: question.type }),
      explanation: question.comment ?? "",

      explanationImage: question.explanationImage ?? null,
      createdAt: new Date(),
      suggestions: extractQuestionSuggestions(question),
    });
  } catch (error) {
    console.log("error while set");

    console.log(error);
  }
  let foundQuestion;
  try {
    foundQuestion = await getQuestionById(newQuestionId);
  } catch (error) {
    console.log("error while getQstById");

    console.log(error);
  }

  try {
    const newQstInsideSubcategoryCollection = firestore
      .collection("Subcategories")
      .doc(subCategory.id)
      .collection("Questions")
      .doc(newQuestionId);
    await newQstInsideSubcategoryCollection.set(foundQuestion);
  } catch (error) {
    console.log("error while update qst");

    console.log(error);
  }

  if (freeIds.length === 0) {
    try {
      await incremetGeneralStatistics(location, "questions");
      await incremetGeneralStatistics("GENERAL", "questions");
    } catch (error) {
      console.log("error while increment");
      console.log(error);
    }
  }
  //console.log("operation success");
};
export const createQuestionForCliniqCase = async (
  question, cliniqCaseId, location, field, category, subCategory) => {



  if (checkEmptyQuestionWithEmptySuggestions(question)) {
    console.log("empty question + empty suggestions");
    return;
  }
  let freeIds = await getFreeIds();

  let extractedFreeId = await extractIdFromFreeIds();

  let extractedIdFromStatisitcs = await getNewQuestionId("GENERAL")

  let newQuestionId = freeIds.length === 0 ? extractedIdFromStatisitcs : extractedFreeId

  const questionsRef = firestore.collection("Questions").doc(newQuestionId);

  try {
    await questionsRef.set({
      cliniqCaseId,
      categoryId: category.id,
      categoryName: category.name,
      subcategoryId: subCategory.id,
      subcategoryName: subCategory.name,
      text: question.Qst,
      year: question.year,
      ...(question.type && { type: question.type }),
      ...(question.type2 && { type2: question.type2 }),
      explanation: question.comment ?? "",
      location,
      explanationImage: question.explanationImage ?? null,
      createdAt: new Date(),
      suggestions: extractQuestionSuggestions(question),
    });
  } catch (error) {
    console.log("error while set");

    console.log(error);
  }
  let foundQuestion;
  try {
    foundQuestion = await getQuestionById(newQuestionId);
  } catch (error) {
    console.log("error while getQstById");

    console.log(error);
  }

  try {
    const newQstInsideSubcategoryCollection = firestore
      .collection("Subcategories")
      .doc(subCategory.id)
      .collection("Questions")
      .doc(newQuestionId);
    await newQstInsideSubcategoryCollection.set(foundQuestion);
  } catch (error) {
    console.log("error while update qst");

    console.log(error);
  }

  if (freeIds.length === 0) {
    try {
      await incremetGeneralStatistics(`${location}_${field}`, "questions");
      await incremetGeneralStatistics("GENERAL", "questions");
    } catch (error) {
      console.log("error while increment");
      console.log(error);
    }
  }
  return foundQuestion
  //console.log("operation success");
};
export const createQuestionAllCases = async (
  question,
  location,
  field,
  category,
  subCategory,
) => {

  if (checkEmptyQuestionWithEmptySuggestions(question)) {
    console.log("empty question + empty suggestions");
    return;
  }


  try {
    if (question.type === "cas clinique") {


      let freeIds = await getFreeIds();
      let newQuestionId =
        freeIds.length === 0
          ? await getNewQuestionId("GENERAL")
          : await extractIdFromFreeIds();


      const questionsRef = firestore.collection("Questions").doc(newQuestionId);
      await questionsRef.set({
        categoryId: category.id,
        categoryName: category.name,
        subcategoryId: subCategory.id,
        subcategoryName: subCategory.name,
        text: question.Qst,
        year: question.year,
        location,
        ...(question.type && { type: question.type }),
        ...(question.type2 && { type2: question.type2 }),
        cliniqCaseId: question.cliniqCaseId,

        explanationImage: question.explanationImage ?? null,
        createdAt: new Date(),

      });



      if (freeIds.length === 0) {
        try {
          await incremetGeneralStatistics(`${location}_${field}`, "questions");
          await incremetGeneralStatistics("GENERAL", "questions");
        } catch (error) {
          console.log("error while increment");
          console.log(error);
        }
      }

      let docs = await mapAsyncSequential(question.subQsts, createQuestionForCliniqCase, newQuestionId, location, field, category, subCategory)

      await questionsRef.update({

        subQsts: docs
      });


      let foundQuestion;
      try {
        foundQuestion = await getQuestionById(newQuestionId);
      } catch (error) {
        console.log("error while getQstById");
        console.log(error);
      }

      try {
        const newQstInsideSubcategoryCollection = firestore
          .collection("Subcategories")
          .doc(subCategory.id)
          .collection("Questions")
          .doc(newQuestionId);
        await newQstInsideSubcategoryCollection.set(foundQuestion);
      } catch (error) {
        console.log("error while update qst");

        console.log(error);
      }
    }
    else {
      let freeIds = await getFreeIds();
      let newQuestionId =
        freeIds.length === 0
          ? await getNewQuestionId("GENERAL")
          : await extractIdFromFreeIds();
      const questionsRef = firestore.collection("Questions").doc(newQuestionId);


      await questionsRef.set({
        categoryId: category.id,
        categoryName: category.name,
        subcategoryId: subCategory.id,
        subcategoryName: subCategory.name,
        text: question.Qst,
        year: question.year,
        location,
        type: question.type ?? null,
        type2: question.type2 ?? null,
        cliniqCaseId: question.cliniqCaseId ?? null,
        explanation: question.comment ?? "",
        explanationImage: question.explanationImage ?? null,
        createdAt: new Date(),
        suggestions: extractQuestionSuggestions(question),
      });


      let foundQuestion;
      try {
        foundQuestion = await getQuestionById(newQuestionId);
      } catch (error) {
        console.log("error while getQstById");

        console.log(error);
      }

      try {
        const newQstInsideSubcategoryCollection = firestore
          .collection("Subcategories")
          .doc(subCategory.id)
          .collection("Questions")
          .doc(newQuestionId);
        await newQstInsideSubcategoryCollection.set(foundQuestion);
      } catch (error) {
        console.log("error while update qst");

        console.log(error);
      }

      if (freeIds.length === 0) {
        try {
          await incremetGeneralStatistics(`${location}_${field}`, "questions");
          await incremetGeneralStatistics("GENERAL", "questions");
        } catch (error) {
          console.log("error while increment");
          console.log(error);
        }
      }
    }

  } catch (error) {
    console.log("error while set");

    console.log(error);
  }

  //console.log("operation success");
};


export const updateQuestionAllCases = async (
  newQuestion,
) => {


  const questionRef = firestore.collection("Questions").doc(newQuestion.id);
  const questionSnap = await questionRef.get();
  if (!questionSnap.exists) {
    console.log("Question not found in Firestore.");
    return;
  }

  let updatedData = {};
  const existingData = questionSnap.data();
  console.log(existingData)

  let subcategoryQuestionRef = firestore.collection("Subcategories").doc(existingData.subcategoryId).collection('Questions').doc(newQuestion.id)
  let updatedSuggestions = existingData.suggestions || [];


  if (newQuestion.Qst !== null) {
    updatedData.text = newQuestion.Qst;
  }

  if (newQuestion.type !== null) {
    updatedData.type = newQuestion.type;
  }
  if (newQuestion.year !== null) {
    updatedData.year = newQuestion.year;
  }


  // Mapping propositions A to F to indices in suggestions
  const propMapping = ["A", "B", "C", "D", 'E', 'F'];


  propMapping.forEach((prop, index) => {


    if (!!newQuestion[prop]) {
      console.log(prop, "exist for update");

      if (updatedSuggestions[index]) {

        updatedSuggestions[index].text = newQuestion[prop]; // Update text
      } else {
        // Create new suggestion if missing
        updatedSuggestions[index] = {
          id: uuidv4(),
          isCorrect: false,
          isSelected: false,
          text: newQuestion[prop],
          isStudentAnswer: false
        };
      }
    }
  });

  // Mark correct answers if CT is present
  if (newQuestion.CT !== null) {
    const correctAnswers = newQuestion.CT.split(""); // Convert CT string into an array of individual letters
    updatedSuggestions.forEach((suggestion, index) => {
      suggestion.isCorrect = correctAnswers.includes(propMapping[index]);
    });
  }

  // Update Firestore
  updatedData.suggestions = updatedSuggestions;

  console.log(updatedData);

  await questionRef.update(updatedData);
  await subcategoryQuestionRef.update(updatedData)
  console.log("Question updated successfully!");
}
//console.log("operation success");



export const createQuestionWithId = async (category, subCategory, question) => {
  let newQuestionId = await getNewQuestionId();
  const questionsRef = firestore.collection("Questions").doc(newQuestionId);

  try {
    await questionsRef.set({
      categoryId: category.id,
      categoryName: category.name,
      subcategoryId: subCategory.id,
      subcategoryName: subCategory.name,
      text: question.Qst,
      year: "2024",
      explanation: question.Comment,
      explanationImage: null,
      createdAt: new Date(),
      suggestions: extractQuestionSuggestions(question),
    });
  } catch (error) {
    console.log(error);
  }
  let foundQuestion;
  try {
    foundQuestion = await getQuestionById(newQuestionId);
  } catch (error) {
    console.log(error);
  }

  try {
    const newQstInsideSubcategoryCollection = firestore
      .collection("Subcategories")
      .doc(subCategory.id)
      .collection("Questions")
      .doc(newQuestionId);
    await newQstInsideSubcategoryCollection.set(foundQuestion);
  } catch (error) {
    console.log(error);
  }

  try {
    await incremetGeneralStatistics("questions");
  } catch (error) {
    console.log(error);
  }

  //console.log("operation success");
};

export const updateQuestionIfExists = async (
  category,
  subCategory,
  question,
  remoteQstId
) => {
  const questionsRef = firestore.collection("Questions").doc(remoteQstId);
  let updateQuestion = {
    categoryId: category.id,
    categoryName: category.name,
    subcategoryId: subCategory.id,
    subcategoryName: subCategory.name,
    text: question.Qst,
    year: parseInt(question["Annee Scolaire"].split("/")[0]),
    explanation: question.Commentaire,
    explanationImage: null,
    createdAt: new Date(),
    suggestions: extractQuestionSuggestions(question),
  };
  try {
    await questionsRef.set(updateQuestion);
  } catch (error) {
    console.log(error);
  }
  try {
    const newQstInsideSubcategoryCollection = firestore
      .collection("Subcategories")
      .doc(subCategory.id)
      .collection("Questions")
      .doc(remoteQstId);
    await newQstInsideSubcategoryCollection.set(updateQuestion);
  } catch (error) {
    console.log(error);
  }

  // console.log("operation success");
};

export async function saveModuleQuestions(moduleName, filePath) {
  try {
    // Make the API request
    const response = await getQuestionByModule(moduleName);
    fs.writeFileSync(filePath, JSON.stringify(response, null, 2));
    // Check if the request was successful (status code 200)
  } catch (error) {
    console.error("Error:", error.message);
  }
}

export async function deleteQuestionsByModule(category, location) {
  const queryRef = firestore
    .collection("Questions")
    .where("categoryName", "==", category)
    .where("location", "==", location);
  const query = await queryRef.get();
  let deletedIds = [];
  if (query.empty) {
    return [];
  }
  query.forEach((doc) => {
    deletedIds.push(doc.id);
  });
  console.log(deletedIds);
  await forEachAsync(deletedIds, deleteQuestion);
  try {
    let freeIdsRef = firestore.collection("FreeIds").doc("free");
    let freeIds = await freeIdsRef.get();
    if (freeIds.exists) {
      let newFreeIds = [...new Set([...freeIds.data().free, ...deletedIds])];
      await freeIdsRef.set({
        free: newFreeIds,
      });
    } else {
      await freeIdsRef.set({
        free: deletedIds,
      });
    }
  } catch (error) {
    console.log(error);
  }
  console.log("all questions deleted");
}
export async function deleteQuestionsByModuleId(category, location) {
  const queryRef = firestore
    .collection("Questions")
    .where("categoryId", "==", category.id)
    .where("location", "==", location);
  const query = await queryRef.get();
  let deletedIds = [];
  if (query.empty) {
    return [];
  }
  query.forEach((doc) => {
    deletedIds.push(doc.id);
  });
  console.log(deletedIds);
  await forEachAsync(deletedIds, deleteQuestion);
  try {
    let freeIdsRef = firestore.collection("FreeIds").doc("free");
    let freeIds = await freeIdsRef.get();
    if (freeIds.exists) {
      let newFreeIds = [...new Set([...freeIds.data().free, ...deletedIds])];
      await freeIdsRef.set({
        free: newFreeIds,
      });
    } else {
      await freeIdsRef.set({
        free: deletedIds,
      });
    }
  } catch (error) {
    console.log(error);
  }

  console.log("all questions deleted");
}
export async function deleteQuestionsBySubcategoryId(subcategoryId, location) {
  const queryRef = firestore
    .collection("Questions")
    .where("subcategoryId", "==", subcategoryId)
    .where("location", "==", location);
  const query = await queryRef.get();
  let deletedIds = [];
  if (query.empty) {
    return [];
  }
  query.forEach((doc) => {
    deletedIds.push(doc.id);
  });
  await forEachAsync(deletedIds, deleteQuestion);
  try {
    let freeIdsRef = firestore.collection("FreeIds").doc("free");
    let freeIds = await freeIdsRef.get();
    if (freeIds.exists) {
      let newFreeIds = [...new Set([...freeIds.data().free, ...deletedIds])];
      await freeIdsRef.set({
        free: newFreeIds,
      });
    } else {
      await freeIdsRef.set({
        free: deletedIds,
      });
    }
  } catch (error) {
    console.log(error);
  }
  console.log("all questions deleted");
}
export async function deleteQuestionsByCourse(subcategory, location) {
  const queryRef = firestore
    .collection("Questions")
    .where("subcategoryName", "==", subcategory)
    .where("location", "==", location);
  const query = await queryRef.get();
  let deletedIds = [];
  if (query.empty) {
    return [];
  }
  query.forEach((doc) => {
    deletedIds.push(doc.id);
  });
  await forEachAsync(deletedIds, deleteQuestion);
  try {
    let freeIdsRef = firestore.collection("FreeIds").doc("free");
    let freeIds = await freeIdsRef.get();
    if (freeIds.exists) {
      let newFreeIds = [...new Set([...freeIds.data().free, ...deletedIds])];
      await freeIdsRef.set({
        free: newFreeIds,
      });
    } else {
      await freeIdsRef.set({
        free: deletedIds,
      });
    }
  } catch (error) {
    console.log(error);
  }
  console.log("all questions deleted");
}

export const addQuestionToModuleV2 = async (question) => {
  //get general statistics (number of modules, course, and questions)

  await createQuestionWithIdV2(location, remoteModule, remoteCourse, question);

  console.log("new question added ");
};


const formatFirebaseQuestionToJsonQuestion = (firebaseQts) => {
  let letters = { "0": "A", "1": "B", "2": "C", "3": "D", "4": "E", "5": "F" }
  let suggestions = {}
  let CT = ''
  firebaseQts.suggestions?.forEach((ele, index) => {
    let letter = letters[index]
    suggestions[letter] = ele.text
    if (ele.isCorrect) {
      CT = CT.concat(letter)
    }
  })

  return {
    "Qst": firebaseQts.text,
    "year": firebaseQts.year,
    ...suggestions,
    CT,
    "comment": firebaseQts.explanation
  }
}

const range = (start, stop) => {
  return new Array(stop - start).fill(0).map((ele, index) => start + index)
}
function getQuestionType(question) {

  let { suggestions } = question
  if (!question.type) {
    console.log('question with no type, !!!!!!')

    if (suggestions.length === 1) {
      return "qroc"
    }
    let answers = suggestions.map(ele => ele.isCorrect)
    if (answers.filter(ele => ele).length > 1) {
      return "qcm"
    } else {
      return 'qcs'
    }
  }
  return question.type

}

async function countMatchingDocs() {
  const questionsRef = firestore.collection("Categories").where("location", "==", "BATNA").where("field", "==", "PHARMACY");

  const countQuery = questionsRef.count();
  const snapshot = await countQuery.get();

  console.log(`total matching documents: ${snapshot.data().count}`);
  return snapshot.data().count;
}


async function countSubcategories() {
  const categoriesRef = firestore.collection("Subcategories")
    .where("location", "==", "ALGER")
    .where("field", "==", "MEDICINE");

  const categoriesSnapshot = await categoriesRef.get();

  if (categoriesSnapshot.empty) {
    console.log("no matching categories found.");
    return 0;
  }

  let totalSubcategories = 0;

  // iterate over each matching category document
  for (const categoryDoc of categoriesSnapshot.docs) {
    const subcategoriesRef = categoryDoc.ref.collection("Subcategories");
    const subcategoriesSnapshot = await subcategoriesRef.count().get(); // efficient count query

    totalSubcategories += subcategoriesSnapshot.data().count;
  }

  console.log(`total subcategories: ${totalSubcategories}`);
  return totalSubcategories;
}

async function getTotalQuestionsCount() {
  const categoriesRef = firestore.collection("Categories")
    .where("location", "==", "BATNA")
    .where("field", "==", "PHARMACY");

  const categoriesSnapshot = await categoriesRef.get();

  if (categoriesSnapshot.empty) {
    console.log("no matching categories found.");
    return 0;
  }

  let subcategoryIds = [];

  // retrieve subcategory ids from category's subcollection
  for (const categoryDoc of categoriesSnapshot.docs) {
    const subcategoriesRef = categoryDoc.ref.collection("Subcategories");
    const subcategoriesSnapshot = await subcategoriesRef.get();

    for (const subcategoryDoc of subcategoriesSnapshot.docs) {
      subcategoryIds.push(subcategoryDoc.id);
    }
  }

  if (subcategoryIds.length === 0) {
    console.log("no subcategories found.");
    return 0;
  }

  let totalQuestions = 0;

  // retrieve and sum question counts for all subcategories
  const questionCounts = await Promise.all(subcategoryIds.map(async (subcategoryId) => {
    const questionsRef = firestore.collection("Subcategories").doc(subcategoryId).collection("Questions");
    const questionsSnapshot = await questionsRef.count().get();
    return questionsSnapshot.data().count;
  }));

  totalQuestions = questionCounts.reduce((sum, count) => sum + count, 0);

  console.log("total number of questions:", totalQuestions);
  return totalQuestions;
}

/* (async () => {

  //course 

  //let subcategoryName = "Anatomie de l'urètre"
  //let subcategory = await getSubcategoryByName(subcategoryName)

  //await deleteSubcategoryWithCatSub({ id: "0GQBvEhDeRFIhIE9MRlt", categoryId: 'Zf3g958R9FBdMqUz59uz' }, "BATNA")
  await deleteQuestionsBySubcategoryId("0zNuPkVKhVpw8FKtXttR", "BATNA")

  // await deleteQuestionsByCourse('Aqueous solution', "BATNA")
  //await deleteSubcategoryWithCatSub({ id: "ovZS8SbnoGDunnxAYCsA", categoryId: 'PCJ75rJI9skPoZDk5TTH' }, "BATNA")

  //Module
  //await deleteQuestionsByModuleId({ id: 'JhnEbsfIguyoNvIQ4LxT' }, "BLIDA")
  //await deleteCategory({ id: "ijIqd5aMYNwFcIE30luD" }, "BATNA")


}) */

  /*const deleteQuestionsForModule = async () => {
    try {
      // Create a category object with an id
      const category = {
        id: "H5euJOI1g1VFCdqscLW2" // Replace with actual category ID
      };
      
      // Specify the location
      const location = "BATNA"; // Replace with actual location value
      
      // Delete all questions for this category and location
      const deletedIds = await deleteQuestionsByModuleId(category, location);
      
      console.log("Deleted question IDs:", deletedIds);
      console.log("Questions deletion completed successfully");
    } catch (error) {
      console.error("Error deleting questions:", error);
    }
  };
  
  // Execute the function
  deleteQuestionsForModule();*/

//Mna81BwR9od0GGVz0o7m urinaire alger 2eme
//BpjJHz73OeBp4FnUHBVF histo
//cwo4EYWbEoqWCMndgyR1 anato
//zmp28xHl1VsR6anrXoxp bio
//vJdE5gRVrTgqAc7e1FoT physio

//endocrino blida
//radiologie dj5hzxEz8DTNWbpBGOK9
//semiologie K83wpfekI2yKWJYhSRkd
//physiopath SZythE4kryTg7HYVc2WP
//biochimie tgAZ7OYivCbVRR3rm2L7


----------------------------------
subcategoryService





import { firestore } from "./config-firebase.js";
import { FieldValue } from "firebase-admin/firestore";
import {
  decrementGeneralStatistics,
  incremetGeneralStatistics,
} from "./statisticsService.js";
import { getQuestionById } from "./questionService.js";
import {
  checkCategoryExists,
  checkCategoryExistsWithFather,
  createCategory,
} from "./categoryService.js";

//const firestore = getFirestore(defaultApp);

export const getSubcategory = async (subCategoryId) => {
  const subCategoryRef = firestore
    .collection("Subcategories")
    .doc(subCategoryId);
  const subCategory = await subCategoryRef.get();
  return { id: subCategory.id, ...subCategory.data() };
};

export const getSubcategoryByName = async (subName) => {
  const subCategoryRef = firestore
    .collection("Subcategories")
    .where("name", "==", subName);
  const category = await subCategoryRef.get();
  if (category.empty) {
    return null;
  }
  const data = [];
  category.forEach((doc) => {
    data.push({ id: doc.id, ...doc.data() });
  });
  if (data.length > 0) return data[0];
  return null;
};
export const createSubcategory = async (
  location,
  field,
  category,
  subcategoryData
) => {
  //CREATE SUBCATEGORY
  const subCategoryRef = firestore.collection("Subcategories");
  let subCategory;
  try {
    subCategory = await subCategoryRef.add({
      categoryId: category.id,
      name: subcategoryData,
      createdAt: new Date(),
      questionsCount: 0,
      location,
      field,
    });
  } catch (error) {
    console.log(error);
  }

  //ADD SUBCATEGORY IN CATEGORIES COLLECTION
  try {
    let subCategoriesRef = firestore
      .collection("Categories")
      .doc(category.id)
      .collection("Subcategories")
      .doc(subCategory.id);
    subCategoriesRef.set({
      categoryId: category.id,
      createdAt: new Date(),
      name: subcategoryData,
      questionsCount: 0,
      location,
    });
  } catch (error) {
    console.log(error);
  }
  //INCREMENT STATISCTICS LIST
  try {
    await incremetGeneralStatistics(`${location}_${field}`, "courses");
    await incremetGeneralStatistics("GENERAL", "courses");
  } catch (error) {
    console.log(error);
  }
  console.log("subcategory added successfully");

  return { name: subcategoryData, id: subCategory.id };
};

export const createOrGetExistingCategoryAndSubcategory = async (
  location,
  categoryName,
  subcategoryName,
  subscriptions,
  studyLevel,
  field
) => {
  let category = await checkCategoryExists(
    location,
    categoryName,
    studyLevel,
    field
  );
  if (!category) {
    category = await createCategory(
      location,
      categoryName,
      subscriptions,
      studyLevel,
      field
    );
  }
  let subcategory = await checkSubcategoryExists(
    location,
    subcategoryName,
    category.id
  );
  if (!subcategory) {
    subcategory = await createSubcategory(location, category, subcategoryName);
  }
  return [category, subcategory];
};

export const createOrGetExistingCategoryAndSubcategoryV2 = async (
  location,
  categoryName,
  father,
  subcategoryName,
  subscriptions,
  studyLevel,
  field
) => {

  let category
  if (father) {

    category = await checkCategoryExistsWithFather(
      location,
      categoryName,
      studyLevel,
      field,
      father
    );

  } else {
    category = await checkCategoryExists(
      location,
      categoryName,
      studyLevel,
      field
    );
  }

  if (!category && father) {
    category = await createCategory(
      location,
      categoryName,
      subscriptions,
      studyLevel,
      field,
      father
    );
  }
  if (!category && !father) {
    category = await createCategory(
      location,
      categoryName,
      subscriptions,
      studyLevel,
      field
    );
  }
  let subcategory = await checkSubcategoryExists(
    location,
    subcategoryName,
    category.id
  );
  if (!subcategory) {
    subcategory = await createSubcategory(location, field, category, subcategoryName);
  }
  return [category, subcategory];
};

export const addSubcategoryQuestion = async (subCategoryId, questionId) => {
  const qst = await getQuestionById(questionId);
  const subCategoryRef = firestore
    .collection("Subcategories")
    .doc(subCategoryId);
  try {
    subCategoryRef.collection("Questions").add(qst);
    console.log("question added to sub cat success");
  } catch (error) {
    console.log(error);
  }
};

export const updateSubcategoryQuestion = async (
  subCategoryId,
  questionId,
  field,
  data
) => {
  const subCategoryQuestionRef = firestore
    .collection("Subcategories")
    .doc(subCategoryId)
    .collection("Questions")
    .doc(questionId);
  try {
    subCategoryQuestionRef.update({
      [field]: data,
    });
  } catch (error) {
    console.log(error);
  }
  console.log("question in subcategory updated");
};

export const getSubcategoryQuestions = async (subCategoryId) => {
  const subCategoryQuestionsRef = firestore
    .collection("Subcategories")
    .doc(subCategoryId)
    .collection("Questions");
  const questions = await subCategoryQuestionsRef.get();
  let data = [];
  questions.forEach((doc) => {
    data.push({ id: doc.id, ...doc.data() });
  });
  return data;
};

export const getSubcategories = async () => {
  const subCategoriesRef = firestore.collection("Subcategories");
  const subCategories = await subCategoriesRef.get();
  let data = [];

  subCategories.forEach((doc) => {
    data.push({ id: doc.id, ...doc.data() });
  });
  return data;
};
export const getSubcategoriesCount = async () => {
  try {
    const questionsREf = firestore.collection("Subcategories");
    const questionSize = await questionsREf.get();
    return questionSize.size;
  } catch (error) { }
};
export const getSubcategoriesCounts = async (categoryId) => {
  try {
    const questionsREf = firestore.collection("Subcategories").where('categoryId', "==", categoryId);
    const questionSize = await questionsREf.get();
    return questionSize.size;
  } catch (error) { }
};
export const updateCategory = async (remoteModule) => {
  /*   let remoteModule = {
    id: "CIxUgMwtudGXfqgaJQQc",
    name: "les épreuves 2024  ",
  }; */
  let catRef = firestore.collection("Categories").doc(remoteModule.id);
  try {
    await catRef.set({
      createdAt: new Date(),
      father: "",
      name: remoteModule.name,
    });
    console.log("category updated succefully ");
  } catch (error) {
    console.log(error);
  }
};
export const getSubcategoriesOfCategory = async (categoryName, location) => {
  const categoryRef = firestore
    .collection("Categories")
    .where("name", "==", categoryName)
    .where('location', '==', location);
  const category = await categoryRef.get();
  if (category.empty) {
    return [];
  }
  const data = [];
  category.forEach((doc) => {
    data.push({ id: doc.id, ...doc.data() });
  });
  const subcategoriesRef = firestore
    .collection("Subcategories")
    .where("categoryId", "==", data[0].id);

  const subcategories = await subcategoriesRef.get();
  if (subcategories.empty) {
    return [];
  }
  const subcategoriesData = [];
  subcategories.forEach((doc) => {
    subcategoriesData.push({ id: doc.id, ...doc.data() });
  });
  return subcategoriesData;
};

export const deleteSubcategoryWithCatSub = async (subcategory, location) => {
  const subcategotyRef = firestore
    .collection("Subcategories")
    .doc(subcategory.id);

  try {
    await subcategotyRef.delete();
    console.log("subcategory deleted from collection");
  } catch (err) {
    console.log(err);
  }
  const subcategoryInCategoryRef = firestore
    .collection("Categories")
    .doc(subcategory.categoryId)
    .collection("Subcategories")
    .doc(subcategory.id);
  try {
    await subcategoryInCategoryRef.delete();
    console.log("subcategory deleted in categories collection");
  } catch (error) { }

  try {
    await decrementGeneralStatistics(location, "courses");
    await decrementGeneralStatistics("GENERAL", "courses");
    console.log("statistics decremented");
  } catch (error) { }
};
export const deleteSubcategory = async (subcategory, location) => {
  const subcategotyRef = firestore
    .collection("Subcategories")
    .doc(subcategory.id);

  try {
    await subcategotyRef.delete();
    console.log("subcategory deleted from collection");
  } catch (err) {
    console.log(err);
  }

  try {
    await decrementGeneralStatistics(location, "courses");
    await decrementGeneralStatistics("GENERAL", "courses");
    console.log("statistics decremented");
  } catch (error) { }
};
export const checkSubcategoryExists = async (
  location,
  subcategory,
  categoryId
) => {
  const docsRef = firestore
    .collection("Subcategories")
    .where("name", "==", subcategory)
    .where("location", "==", location)
    .where("categoryId", "==", categoryId);
  let docsData = await docsRef.get();
  if (docsData.empty) {
    return null;
  }
  let data = [];
  docsData.forEach((doc) => {
    data.push({ id: doc.id, name: doc.data().name });
  });
  return data[0];
};

export const upateSubcategoryLocation = async (subId) => {
  try {
    await firestore.collection("Subcategories").doc(subId).update({
      location: "BATNA",
    });
  } catch (error) {
    console.log(error);
  }
  console.log("subcategory updated");
};

