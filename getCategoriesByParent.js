import { firestore } from './services/firebase/config-firebase.js';
import readline from 'readline';
import dotenv from 'dotenv';
dotenv.config();

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Get categories by parent category name and wilaya (location)
 * @param {string} parentCategoryName - Name of the parent category
 * @param {string} wilaya - Location/wilaya to filter by
 * @returns {Array} Array of child categories with their IDs and subcategories
 */
async function getCategoriesByParent(parentCategoryName, wilaya) {
    try {
        console.log(`\n🔍 Searching for child categories under parent: "${parentCategoryName}" in wilaya: "${wilaya}"`);
        
        // Query categories where father field equals the parent category name and location matches wilaya
        const categoriesRef = firestore
            .collection("Categories")
            .where("father", "==", parentCategoryName)
            .where("location", "==", wilaya);
        
        const categoriesSnapshot = await categoriesRef.get();
        
        if (categoriesSnapshot.empty) {
            console.log(`❌ No child categories found under parent "${parentCategoryName}" in wilaya "${wilaya}"`);
            return [];
        }
        
        const categories = [];
        categoriesSnapshot.forEach((doc) => {
            categories.push({ id: doc.id, ...doc.data() });
        });
        
        console.log(`✅ Found ${categories.length} child categories under parent "${parentCategoryName}" in wilaya "${wilaya}"`);
        
        // For each category, also get its subcategories
        const categoriesWithSubcategories = [];
        
        for (const category of categories) {
            console.log(`\n📂 Category: "${category.name}" (ID: ${category.id})`);
            console.log(`   - Location: ${category.location}`);
            console.log(`   - Study Level: ${category.studyLevel || 'Not specified'}`);
            console.log(`   - Field: ${category.field || 'Not specified'}`);
            console.log(`   - Parent: ${category.father}`);
            console.log(`   - Created: ${category.createdAt ? new Date(category.createdAt.seconds * 1000).toLocaleDateString() : 'Not specified'}`);
            
            // Get subcategories for this category
            try {
                const subcategoriesRef = firestore
                    .collection("Subcategories")
                    .where("categoryId", "==", category.id);
                
                const subcategoriesSnapshot = await subcategoriesRef.get();
                const subcategories = [];
                
                subcategoriesSnapshot.forEach((doc) => {
                    subcategories.push({ id: doc.id, ...doc.data() });
                });
                
                if (subcategories.length > 0) {
                    console.log(`   📋 Subcategories (${subcategories.length}):`);
                    subcategories.forEach((sub, index) => {
                        console.log(`      ${index + 1}. "${sub.name}" (ID: ${sub.id}) - Questions: ${sub.questionsCount || 0}`);
                    });
                } else {
                    console.log(`   📋 No subcategories found`);
                }
                
                categoriesWithSubcategories.push({
                    ...category,
                    subcategories: subcategories
                });
                
            } catch (error) {
                console.error(`   ❌ Error fetching subcategories for category ${category.id}:`, error.message);
                categoriesWithSubcategories.push({
                    ...category,
                    subcategories: []
                });
            }
        }
        
        return categoriesWithSubcategories;
        
    } catch (error) {
        console.error('❌ Error fetching categories by parent:', error);
        throw error;
    }
}

/**
 * Get all available parent categories for a given wilaya
 * @param {string} wilaya - Location/wilaya to filter by
 * @returns {Array} Array of unique parent category names
 */
async function getAvailableParentCategories(wilaya) {
    try {
        const categoriesRef = firestore
            .collection("Categories")
            .where("location", "==", wilaya);
        
        const categoriesSnapshot = await categoriesRef.get();
        
        if (categoriesSnapshot.empty) {
            return [];
        }
        
        const parentCategories = new Set();
        categoriesSnapshot.forEach((doc) => {
            const data = doc.data();
            if (data.father && data.father !== "" && data.father !== "yes") {
                parentCategories.add(data.father);
            }
        });
        
        return Array.from(parentCategories).sort();
        
    } catch (error) {
        console.error('Error fetching parent categories:', error);
        return [];
    }
}

/**
 * Get all available wilayas
 * @returns {Array} Array of unique wilaya names
 */
async function getAvailableWilayas() {
    try {
        const categoriesRef = firestore.collection("Categories");
        const categoriesSnapshot = await categoriesRef.get();
        
        if (categoriesSnapshot.empty) {
            return [];
        }
        
        const wilayas = new Set();
        categoriesSnapshot.forEach((doc) => {
            const data = doc.data();
            if (data.location) {
                wilayas.add(data.location);
            }
        });
        
        return Array.from(wilayas).sort();
        
    } catch (error) {
        console.error('Error fetching wilayas:', error);
        return [];
    }
}

/**
 * Interactive function to get user input and display results
 */
async function interactiveSearch() {
    try {
        console.log('🏛️  Category Hierarchy Explorer');
        console.log('=====================================\n');
        
        // Get available wilayas
        console.log('📍 Fetching available wilayas...');
        const wilayas = await getAvailableWilayas();
        
        if (wilayas.length === 0) {
            console.log('❌ No wilayas found in the database.');
            rl.close();
            return;
        }
        
        console.log('Available wilayas:');
        wilayas.forEach((wilaya, index) => {
            console.log(`  ${index + 1}. ${wilaya}`);
        });
        
        rl.question('\nEnter wilaya name (or number): ', async (wilayaInput) => {
            let selectedWilaya;
            
            // Check if input is a number
            const wilayaIndex = parseInt(wilayaInput) - 1;
            if (!isNaN(wilayaIndex) && wilayaIndex >= 0 && wilayaIndex < wilayas.length) {
                selectedWilaya = wilayas[wilayaIndex];
            } else {
                selectedWilaya = wilayaInput.trim();
            }
            
            // Validate wilaya
            if (!wilayas.includes(selectedWilaya)) {
                console.log(`❌ Invalid wilaya: "${selectedWilaya}". Please choose from the available options.`);
                rl.close();
                return;
            }
            
            console.log(`\n📍 Selected wilaya: ${selectedWilaya}`);
            
            // Get available parent categories for this wilaya
            console.log('\n🔍 Fetching available parent categories...');
            const parentCategories = await getAvailableParentCategories(selectedWilaya);
            
            if (parentCategories.length === 0) {
                console.log(`❌ No parent categories found for wilaya "${selectedWilaya}".`);
                rl.close();
                return;
            }
            
            console.log('Available parent categories:');
            parentCategories.forEach((parent, index) => {
                console.log(`  ${index + 1}. ${parent}`);
            });
            
            rl.question('\nEnter parent category name (or number): ', async (parentInput) => {
                let selectedParent;
                
                // Check if input is a number
                const parentIndex = parseInt(parentInput) - 1;
                if (!isNaN(parentIndex) && parentIndex >= 0 && parentIndex < parentCategories.length) {
                    selectedParent = parentCategories[parentIndex];
                } else {
                    selectedParent = parentInput.trim();
                }
                
                // Validate parent category
                if (!parentCategories.includes(selectedParent)) {
                    console.log(`❌ Invalid parent category: "${selectedParent}". Please choose from the available options.`);
                    rl.close();
                    return;
                }
                
                console.log(`\n📂 Selected parent category: ${selectedParent}`);
                
                // Get and display results
                const results = await getCategoriesByParent(selectedParent, selectedWilaya);
                
                if (results.length > 0) {
                    console.log(`\n📊 Summary:`);
                    console.log(`   - Parent Category: ${selectedParent}`);
                    console.log(`   - Wilaya: ${selectedWilaya}`);
                    console.log(`   - Child Categories Found: ${results.length}`);
                    
                    const totalSubcategories = results.reduce((sum, cat) => sum + cat.subcategories.length, 0);
                    console.log(`   - Total Subcategories: ${totalSubcategories}`);
                }
                
                rl.close();
            });
        });
        
    } catch (error) {
        console.error('❌ Error in interactive search:', error);
        rl.close();
    }
}

// Command line arguments support
const args = process.argv.slice(2);

if (args.length >= 2) {
    // Direct execution with command line arguments
    const parentCategory = args[0];
    const wilaya = args[1];
    
    console.log('🏛️  Category Hierarchy Explorer (CLI Mode)');
    console.log('==========================================\n');
    
    getCategoriesByParent(parentCategory, wilaya)
        .then((results) => {
            if (results.length > 0) {
                console.log(`\n📊 Summary:`);
                console.log(`   - Parent Category: ${parentCategory}`);
                console.log(`   - Wilaya: ${wilaya}`);
                console.log(`   - Child Categories Found: ${results.length}`);
                
                const totalSubcategories = results.reduce((sum, cat) => sum + cat.subcategories.length, 0);
                console.log(`   - Total Subcategories: ${totalSubcategories}`);
            }
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Error:', error);
            process.exit(1);
        });
} else {
    // Interactive mode
    interactiveSearch();
}
