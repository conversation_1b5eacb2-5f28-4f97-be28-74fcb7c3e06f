// Base configuration for paths and settings
export const config = {
    // Base paths
    BASE_PROJECT_PATH: '/home/<USER>/Downloads/bmset-upload-scripts-mainlast/bmset-upload-scripts-main',

    // Project structure settings
    location: 'BATNA',
    field: 'MEDICINE',
    module: 'CARDIO',
    year: 3,
    subDirQcm: 'RESIDANAT',

    // File settings
    filesType: 'docx',
    test: false,
    withChapter: true,
    oneCommentFile: false,
    useCommentIndex: false,
    useCommentOrder: true,

    // Upload settings
    upload: false,
    update: false,
    source: 'résidanat'
};

// Helper function to generate subscription ID
export const subsctiptionFreeId = (location, profile) => {
    return `FREE ${location} ${profile}`;
};

// Generate paths based on configuration
export const generatePaths = (config) => {
    const {
        BASE_PROJECT_PATH,
        location,
        field,
        year,
        module,
        subDirQcm
    } = config;

    const subDirComm = subDirQcm;
    const subDirJsonQcm = `json_${subDirQcm}`;

    return {
        commentFolder: `${BASE_PROJECT_PATH}/${location}/${field}/${year}-year/comments/${module.toUpperCase()}/${subDirComm.toUpperCase() ?? ""}`,
        qcmFolder: `${BASE_PROJECT_PATH}/${location}/${field}/${year}-year/qcm/${module.toUpperCase()}/${subDirQcm.toUpperCase()}`,
        jsonQcmFolder: `${BASE_PROJECT_PATH}/${location}/${field}/${year}-year/qcm/${module.toUpperCase()}/${subDirJsonQcm.toUpperCase()}`,
        subscriptions: location === "BATNA" ? [
            //subsctiptionFreeId(location, field),
            "Résidanat 2025 V2", "Résidanat 2026 V2",
            `${year === 1 ? "1ère" : `${year}ème`} année médecine V2`,
            //`${year === 1 ? "1ère" : `${year}ème`} année médecine V2: Réduction 2025`,
            //'1ère + 2ème année Médecine'
        ] : [
            subsctiptionFreeId(location, field),
            "Résidanat 2025 V2", "Résidanat 2026 V2",
            `${year === 1 ? "1ère" : `${year}ème`} année médecine V2`
        ]
    };
};