import "./config-firebase.js";
import admin from "firebase-admin";
import { v4 as uuidv4 } from "uuid";
import { getAllFilesInDirectory } from "../file_system/fileSystem.js";
import { mapAsync } from "../functions/async-map.js";
import { saveToFileSystem } from "../functions/save.js";

// Initialize storage bucket with explicit name when in emulator mode
let bucket;
try {
  if (process.env["FIREBASE_STORAGE_EMULATOR_HOST"]) {
    // For emulator only - won't affect production
    bucket = admin.storage().bucket("bmset-85f8a.appspot.com");
  } else {
    // This is your current production code
    bucket = admin.storage().bucket();
  }
} catch (error) {
  console.error("Error initializing storage bucket:", error);
}

/* let course  = files.commentsFilePath.split('/').pop().split(".")[1].toLowerCase()
let folder =  files.commentsFilePath.split('/').reverse()[1].toLowerCase()
let year = files.commentsFilePath.split('/').reverse()[2][0]
 */
//const destinationPath = `images/${year}-year/${folder}/${course}`; // Destination path in Firebase Storage
export async function uploadImage(localFilePath) {
  const metadata = {
    metadata: {
      // This line is very important. It's to create a download token.
      firebaseStorageDownloadTokens: uuidv4(),
    },
    contentType: "image/png",
    cacheControl: "public, max-age=31536000",
  };
  let fileDestination = `${destinationPath}/${localFilePath.split("/").pop()}`;
  let filename = localFilePath.split("/").pop();

  let remoteFile = bucket.file(fileDestination);

  try {
    const [exists] = await remoteFile.exists();
    if (exists) {
      // If the file exists, get its signed URL
      const [url] = await remoteFile.getSignedUrl({
        action: "read",
        expires: "03-09-2491", // Replace with an expiration date in the future
      });

      return url
    }
    let [File] = await bucket.upload(localFilePath, {
      // Support for HTTP requests made with `Accept-Encoding: gzip`
      gzip: true,
      metadata: metadata,
      destination: fileDestination,
    });

    const url = await File.getSignedUrl({
      action: "read",
      expires: "03-09-2491", // Replace with an expiration date in the future
    });

    return url[0];
  } catch (error) {
    console.log(error);
  }
  // Uploads a local file to the bucket

}

export async function uploadImageV3(localFilePath, _destinationPath) {
  const metadata = {
    metadata: {
      // This line is very important. It's to create a download token.
      firebaseStorageDownloadTokens: uuidv4(),
    },
    contentType: "image/png",
    cacheControl: "public, max-age=31536000",
  };
  let fileDestination = `${_destinationPath}/${localFilePath.split("/").pop()}`;
  let filename = localFilePath.split("/").pop();
  //console.log("upload to",fileDestination)
  let remoteFile = bucket.file(fileDestination);

  try {
    const [exists] = await remoteFile.exists();
    if (exists) {
      // If the file exists, get its signed URL
      const [url] = await remoteFile.getSignedUrl({
        action: "read",
        expires: "03-09-2491", // Replace with an expiration date in the future
      });

      return url
    }
    let [File] = await bucket.upload(localFilePath, {
      // Support for HTTP requests made with `Accept-Encoding: gzip`
      gzip: true,
      metadata: metadata,
      destination: fileDestination,
    });

    const url = await File.getSignedUrl({
      action: "read",
      expires: "03-09-2491", // Replace with an expiration date in the future
    });

    return url[0];
  } catch (error) {
    console.log(error);
  }
  // Uploads a local file to the bucket

}


export async function separateUploadImage(localFilePath, _destinationPath) {
  const metadata = {
    metadata: {
      // This line is very important. It's to create a download token.
      firebaseStorageDownloadTokens: uuidv4(),
    },
    contentType: "image/png",
    cacheControl: "public, max-age=31536000",
  };
  let fileDestination = `${_destinationPath}/${localFilePath.split("/").pop()}`;
  let filename = localFilePath.split("/").pop();

  let remoteFile = bucket.file(fileDestination);

  try {
    const [exists] = await remoteFile.exists();
    if (exists) {
      // If the file exists, get its signed URL
      const [url] = await remoteFile.getSignedUrl({
        action: "read",
        expires: "03-09-2491", // Replace with an expiration date in the future
      });

      return url
    }
    let [File] = await bucket.upload(localFilePath, {
      // Support for HTTP requests made with `Accept-Encoding: gzip`
      gzip: true,
      metadata: metadata,
      destination: fileDestination,
    });

    const url = await File.getSignedUrl({
      action: "read",
      expires: "03-09-2491", // Replace with an expiration date in the future
    });

    return url[0];
  } catch (error) {
    console.log(error);
  }
  // Uploads a local file to the bucket

}

/*(async () => {
  let folder = "/home/<USER>/Downloads/bmset-upload-scripts-mainlast/bmset-upload-scripts-main/BLIDA/MEDICINE/3-year/qcm/ANATOMOPATHOLOGIE/BANQ/tumeurs embryonnaires"
  let images = await getAllFilesInDirectory(folder, ".png")
  let remote = await mapAsync(images, separateUploadImage, "images/BLIDA/MEDICINE/3-year/Anatomopathologie")
  await saveToFileSystem(remote, "images.json")
  //let img = await separateUploadImage(, "images/BATNA/PHARMACY/1-year/Physique")

}) (); */
