import { createStatisctics } from "../firebase/statisticsService.js"
import { addSubscription, createFreeSubscription, createSubscriptionFromData } from "../firebase/subscriptionService.js"
import fs from "fs"
import { createQuestionWithIdV2 } from "../firebase/questionService.js";
import { createOrGetExistingCategoryAndSubcategory } from "../firebase/subcategoryService.js";
import { forEachAsync } from "../functions/async-foreach.js";
//import { createCourse, createCourseSection } from "../firebase/courseService.js";
import { FieldValue, Timestamp, getFirestore } from "firebase-admin/firestore";

console.log("Checking emulator connection...");
console.log("FIRESTORE_EMULATOR_HOST:", process.env["FIRESTORE_EMULATOR_HOST"]);
console.log("Project ID being used:", firestore._projectId);

await createFreeSubscription("BATNA")
await createFreeSubscription("ALGER")
await createFreeSubscription("BLIDA")

await createSubscriptionFromData(1, "BLIDA", false);
await createSubscriptionFromData(2, "BLIDA", false);
await createSubscriptionFromData(3, "BLIDA", false);
await createSubscriptionFromData(4, "BLIDA", false);
await createSubscriptionFromData(5, "BLIDA", false); 
await createSubscriptionFromData(6, "BLIDA", false);
await createSubscriptionFromData([1, 2, 3, 4, 5, 6, 7], "BLIDA", true); 

await createSubscriptionFromData(1, "ALGER", false);
await createSubscriptionFromData(2, "ALGER", false);
await createSubscriptionFromData(3, "ALGER", false);
await createSubscriptionFromData(4, "ALGER", false);
await createSubscriptionFromData(5, "ALGER", false); 
await createSubscriptionFromData(6, "ALGER", false);
await createSubscriptionFromData([1, 2, 3, 4, 5, 6, 7], "ALGER", true);

await createSubscriptionFromData(1, "TIZIOUZOU", false);
await createSubscriptionFromData(2, "TIZIOUZOU", false);
await createSubscriptionFromData(3, "TIZIOUZOU", false);
await createSubscriptionFromData(4, "TIZIOUZOU", false);
await createSubscriptionFromData(5, "TIZIOUZOU", false); 
await createSubscriptionFromData(6, "TIZIOUZOU", false);
await createSubscriptionFromData([1, 2, 3, 4, 5, 6, 7], "TIZIOUZOU", true);


await createStatisctics("GENERAL", {
  courses: 0,
  cours: 0,
  modules: 0,
  questions: 0,
}) 

await createStatisctics("BLIDA_MEDICINE", {
  courses: 2574,
  cours: 102,
  modules: 244,
  questions: 56178,
})

await createStatisctics("TIZIOUZOU_MEDICINE", {
  courses: 0,
  cours: 0,
  modules: 0,
  questions: 0,
})

await createStatisctics("ALGER_MEDICINE", {
  courses: 0,
  cours: 0,
  modules: 0,
  questions: 0,
})

/* await createStatisctics("BATNA_PHARMACY", {
  courses: 0,
  cours: 0,
  modules: 0,
  questions: 0,
})
await createStatisctics("ALGER_MEDICINE", {
  courses: 1268,
  cours: 204,
  modules: 119,
  questions: 40644,
}) */
/* await createStatisctics("BATNA")
await createStatisctics("ALGER")
await createStatisctics("GeneralStatistics") */

console.log("Data initialization complete. Check the emulator UI at http://localhost:4000");
