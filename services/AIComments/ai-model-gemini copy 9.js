import { firestore } from "../firebase/config-firebase.js";
import { getQuestionByCoursId, getQuestionsByModuleId, updateQuestionV2 } from "../firebase/questionService.js";
import { saveToFileSystem } from "../functions/save.js";
import * as fs from "fs"
import { forEachAsync } from "../functions/async-foreach.js";
import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI("AIzaSyDMPxdv2IbifPDLZH6rOguUjcJlP5g4rB8");

let suggestionsIndexMCQ = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];
let totalTokensUsed = 0;
let requestCount = 0;

// NEW: A map of placeholders to their full inline style counterparts.
const stylePlaceholders = {
    'class="BMSET_WARNING"': 'style="background-color: #fff5f5; border-left: 5px solid #e53e3e; padding: 12px 16px; margin-bottom: 24px; border-radius: 4px;"',
    'class="BMSET_WARNING_H4"': 'style="font-weight: bold; color: #9b2c2c; margin: 0 0 8px 0;"',
    'class="BMSET_WARNING_P"': 'style="margin: 0; color: #c53030; font-size: 15px; line-height: 1.6;"',
    'class="SECTION_TITLE"': 'style="font-size: 1.25rem; font-weight: bold; color: #1a202c; border-bottom: 2px solid #718096; padding-bottom: 8px; margin-bottom: 16px; margin-top: 16px;"',
    'class="ANALYSIS_CARD"': 'style="border: 1px solid #e2e8f0; border-radius: 6px; padding: 12px; margin-bottom: 12px;"',
    'class="PROPOSITION_TEXT_VRAI"': 'style="padding: 8px 12px; border-radius: 4px; margin: 0; font-size: 15px; line-height: 1.6; background-color: #e6fffa; color: #234e52;"',
    'class="PROPOSITION_TEXT_FAUX"': 'style="padding: 8px 12px; border-radius: 4px; margin: 0; font-size: 15px; line-height: 1.6; background-color: #fff5f5; color: #9b2c2c;"',
    'class="EXPLANATION_TEXT"': 'style="margin: 12px 0 0 0; font-size: 15px; color: #4a5568; line-height: 1.6;"',
    'class="POINTS_CLES"': 'style="background-color: #fefcbf; border-left: 5px solid #f6e05e; padding: 12px 16px; margin-top: 16px;"',
    'class="POINTS_CLES_H4"': 'style="font-weight: bold; color: #975a16; margin: 0 0 8px 0;"',
    'class="POINTS_CLES_UL"': 'style="margin: 0; padding-left: 0; color: #744210; font-size: 15px; line-height: 1.6; list-style-type: none;"',
    'class="POINTS_CLES_LI"': 'style="margin-bottom: 8px;"',
    'class="FLEX_CONTAINER"': 'style="display: flex; flex-direction: row; flex-wrap: wrap; gap: 16px; margin-top: 24px; justify-content: center;"',
    'class="FLEX_PANEL"': 'style="flex: 1 1 300px; border: 1px solid #e2e8f0; border-radius: 8px; padding: 12px; display: flex; flex-direction: column; align-items: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05);"',
    'class="FLEX_PANEL_COMPARE"': 'style="flex: 1 1 300px; background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 12px; display: flex; flex-direction: column; align-items: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05);"',
    'class="PANEL_TITLE"': 'style="font-size: 16px; font-weight: bold; color: #2d3748; margin-bottom: 12px;"',
    'class="FLOW_ARROW"': 'style="font-size: 20px; color: #4a5568; margin-bottom: 8px;"',
    'class="FLOW_COLUMN"': 'style="display: flex; flex-direction: column; align-items: center; margin: 24px 0;"',
    'class="NODE_DEFAULT"': 'style="border-radius: 4px; padding: 10px 15px; margin-bottom: 8px; text-align: center; font-size: 14px; width: 300px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); background-color: #e6fffa; border: 1px solid #81e6d9; color: #234e52;"'
};

function filteredSuggestions(suggestions) {
  return suggestions.filter(
    (ele) =>
      !ele.text.includes(
        'Veuillez cocher cette option. De cette façon, vous ne serez pas pénalisé par un point incorrect'
      )
  )
}

function getQuestionCorrectAnswers(suggestions) {
  return suggestions
    .map((ele, index) => ({ index, isCorrect: ele.isCorrect }))
    .filter(ele => ele.isCorrect)
    .map(ele => suggestionsIndexMCQ[ele.index])
}

const getSystemInstructionMCQ = (module, cours) => `
You are a medical expert in ${module}, helping students understand clinical MCQ questions from the course "${cours}".
Your entire output must be a single, clean block of HTML.
Your output must be in professional, academic French.
Do NOT use emojis or conversational filler.
All text formatting must use HTML tags and the placeholder classes provided. Do NOT use inline styles or markdown.
`;

// UPDATED: Prompt now uses placeholder classes to minimize AI output tokens.
const generateMCQPrompt = (question, module, cours, options, correctAnswers) => {

  const formattedOptions = options
      .map((ele, index) => `${suggestionsIndexMCQ[index]}: ${ele.text}`)
      .join('\n');

      const promptFinalV9 = `
// --- CRITICAL RULES --- //
1.  **Output:** Generate clean HTML using the placeholder classes below (e.g., <div class="ANALYSIS_CARD">). Do NOT output any inline "style" attributes, except where specified for nodes and bars in the Résumé Visuel.
2.  **Structure:** Follow the required order: Avertissement (optional), Analyse, Points Clés, Résumé Visuel.
3.  **Conditional Avertissement:** You MUST ONLY use the "Avertissement" block if you are certain the provided 'Réponse(s) correcte(s)' is wrong or incomplete. If you agree with the provided answer, DO NOT include the "Avertissement" block. Start the response directly with the "Analyse détaillée des propositions" section.
4.  **Keyword Highlighting:** Use <strong> tags with specific color styles for keywords:
    - <strong style="color: #2b6cb0;">TEXT</strong> for processes/pathophysiology.
    - <strong style="color: #6b46c1;">TEXT</strong> for clinical signs/consequences.
    - <strong style="color: #1a202c;">TEXT</strong> for other important terms.
    - <strong style="color: #dd6b20;">TEXT</strong> for keywords in the "Points Clés" section.

// --- HTML STRUCTURE TO FOLLOW (USING PLACEHOLDER CLASSES) --- //

// (Conditional) Section I: Avertissement
<div class="BMSET_WARNING">
    <h4 class="BMSET_WARNING_H4">Avertissement de l'équipe BMSET</h4>
    <p class="BMSET_WARNING_P">La correction officielle [...] <strong style="color: #68D391;">[Suggested Answer]</strong>. [...]</p>
</div>

// Section II: Analyse détaillée
<h3 class="SECTION_TITLE">Analyse détaillée des propositions</h3>
<div class="ANALYSIS_CARD">
    <p class="PROPOSITION_TEXT_VRAI"><strong>[Proposition Text] (VRAI)</strong></p>
    <p class="EXPLANATION_TEXT">[Explanation with highlighted keywords]</p>
</div>
<div class="ANALYSIS_CARD">
    <p class="PROPOSITION_TEXT_FAUX"><strong>[Proposition Text] (FAUX)</strong></p>
    <p class="EXPLANATION_TEXT">[Explanation with highlighted keywords]</p>
</div>

// Section III: Points Clés & Astuces
<div class="POINTS_CLES">
    <h4 class="POINTS_CLES_H4">Points Clés & Astuces ECN</h4>
    <ul class="POINTS_CLES_UL">
        <li class="POINTS_CLES_LI"><strong>-</strong> [Bullet point text]</li>
    </ul>
</div>

// Section IV: Résumé Visuel
<h3 class="SECTION_TITLE">Résumé Visuel</h3>
// --- INSTRUCTIONS FOR VISUAL SUMMARY LAYOUT --- //
// 1. Analyze the topic. Choose the BEST layout from the three options below.
//    - Use "Layout A" for classifying 2-4 subtypes (e.g., types of thorax).
//    - Use "Layout B" for directly comparing two distinct items (e.g., Stridor vs. Sifflement).
//    - Use "Layout C" as a default for single, linear processes.
// 2. You MUST use one of these layouts. Do not invent a new one.

// --- Layout A: Multi-Panel Mini-Flowcharts --- //
<div class="FLEX_CONTAINER">
    <div class="FLEX_PANEL">
        <h4 class="PANEL_TITLE">[Panel Title]</h4>
        <div style="border-radius: 4px; padding: 10px 15px; margin-bottom: 8px; text-align: center; font-size: 14px; width: 90%; box-shadow: 0 2px 4px rgba(0,0,0,0.05); background-color: [COLOR]; border: 1px solid [COLOR]; color: [COLOR];">[Node Text]</div>
        <div class="FLOW_ARROW">↓</div>
    </div>
</div>

// --- Layout B: Side-by-Side Comparison Chart --- //
<div class="FLEX_CONTAINER">
    <div class="FLEX_PANEL_COMPARE">
        <h4 class="PANEL_TITLE" style="color: [TITLE_COLOR];">[COLUMN TITLE]</h4>
        <div style="border-radius: 6px; padding: 12px; margin-bottom: 10px; text-align: center; font-size: 15px; width: 95%; background-color: [BAR_COLOR]; color: [TEXT_COLOR];">[Info Text]</div>
    </div>
</div>

// --- Layout C: Single-Column Flowchart (for linear processes) --- //
<div class="FLOW_COLUMN">
    <div class="NODE_DEFAULT">[Node Text]</div>
    <div class="FLOW_ARROW">↓</div>
</div>
// --- Keyword Highlighting Rules for Résumé Visuel:
// - Keywords inside flowchart nodes and comparison bars MUST be bolded.
// - You can freely use all established keyword colors.

---
Question:
${question}

Options:
${formattedOptions}

Réponse(s) correcte(s) : ${correctAnswers.join(', ')}
`;

return promptFinalV9;
};

const getSystemInstructionOpen = (module, cours) => `
You are a medical expert in ${module} for the course "${cours}", providing model answers to open-ended clinical questions.
Your entire output must be a single block of clean, structured HTML.
Use semantic HTML tags like <h2>, <h3>, <p>, <ul>, <li>.
Do NOT include <html>, <head>, or <body> tags.
Your entire response must be in French.
`;

const generateOpenEndedPrompt = (question, module, cours) => `
// --- REQUIRED HTML STRUCTURE --- //
<div class="p-3 mb-3">
  <h2 class="text-lg font-bold mb-3">${cours}</h2>
  <p>Résumé structuré du cours : définitions, classifications, tableaux, formules, etc.</p>
</div>

<div class="p-3 mb-3">
  <h2 class="text-lg font-bold mb-3">Explication des termes médicaux</h2>
  <ul>
    <li><strong>Mot-clé :</strong> définition courte et claire.</li>
  </ul>
</div>

<div class="p-3 mb-3">
  <h2 class="text-lg mb-3 font-bold">Réponse typique</h2>
  <p>Réponse détaillée, complète, avec justification médicale, analogies et exemples si pertinents.</p>
</div>

// --- QUESTION TO ANSWER --- //
Question: ${question}
`;

async function explainMCQ(question, module, cours, options, correctAnswers) {
  const model = genAI.getGenerativeModel({
    model: "gemini-2.5-flash",
    systemInstruction: getSystemInstructionMCQ(module, cours),
  });
  const prompt = generateMCQPrompt(question, module, cours, options, correctAnswers);

  const result = await model.generateContent(prompt);
  const response = await result.response;
  
  const usageMetadata = result.response.usageMetadata;
  if (usageMetadata) {
    totalTokensUsed += usageMetadata.totalTokenCount;
    console.log('MCQ Token Usage:', {
      promptTokens: usageMetadata.promptTokenCount,
      candidatesTokens: usageMetadata.candidatesTokenCount,
      totalTokens: usageMetadata.totalTokenCount
    });
  }
  
  let html = response.text();

  // NEW: Post-processing step to replace placeholders with inline styles.
  for (const [placeholder, style] of Object.entries(stylePlaceholders)) {
    const regex = new RegExp(placeholder.replace(/"/g, '\\"'), 'g');
    html = html.replace(regex, style);
  }
  
  return { html };
}

async function explainOpen(question, module, cours) {
  const model = genAI.getGenerativeModel({
    model: "gemini-2.5-flash",
    systemInstruction: getSystemInstructionOpen(module, cours),
  });
  const prompt = generateOpenEndedPrompt(question, module, cours);

  const result = await model.generateContent(prompt);
  const response = await result.response;
  
  const usageMetadata = result.response.usageMetadata;
  if (usageMetadata) {
    totalTokensUsed += usageMetadata.totalTokenCount;
    console.log('Open Question Token Usage:', {
      promptTokens: usageMetadata.promptTokenCount,
      candidatesTokens: usageMetadata.candidatesTokenCount,
      totalTokens: usageMetadata.totalTokenCount
    });
  }
  
  const html = response.text();
  return { html };
}

async function updateQuestionComment(questionData) {
  console.log("updating question ======> ", questionData.id);
  
  if (questionData.explanation) {
    const hasAIContent = [
      "Analyse détaillée des propositions",
      "Points Clés & Astuces", 
      "ECN",
      "résumé visuel"
    ].some(keyword => questionData.explanation.includes(keyword));
    
    if (hasAIContent) {
      console.log(`Skipping question ${questionData.id} - already has AI explanation`);
      // return;
    }
  }
  
  requestCount++;

  try {
    if (questionData.type !== "qroc" || questionData.type == null) {
      const aiExplanation = await explainMCQ(
        questionData.text,
        questionData.categoryName,
        questionData.subcategoryName,
        filteredSuggestions(questionData.suggestions),
        getQuestionCorrectAnswers(questionData.suggestions)
      );
      updateQuestionV2(questionData.id, 'explanation', aiExplanation.html);
    } else {
      const aiExplanation = await explainOpen(
        questionData.text,
        questionData.categoryName,
        questionData.subcategoryName
      );
      updateQuestionV2(questionData.id, 'explanation', aiExplanation.html);
    }
    
    console.log(`Request ${requestCount} completed. Total tokens used so far: ${totalTokensUsed}`);
  } catch (error) {
    console.log(error);
  }
}

// Your list of IDs remains untouched
//9hGp3s7MigxO7s7mYidN
//bAGfyf808DlgvjqjypjZ
//gYwLryv4qOYoJyCGAeNb
//ILSqo36AL4XgmJY93CyP
//86bYMtfcF27j7RJOwpCz

(async () => {
  // Your execution logic remains untouched
  let moduleId = "PvuyDF4YQawYIoGUsww"
  let subcatgoryId = "CDqDBWeUqwNeg5bmwzUg"
  let questions = await getQuestionByCoursId(subcatgoryId)
  
  await forEachAsync(questions, updateQuestionComment)
})();

