import { firestore } from "../firebase/config-firebase.js";
import { getQuestionByCoursId, getQuestionsByModuleId, updateQuestionV2 } from "../firebase/questionService.js";
import { saveToFileSystem } from "../functions/save.js";
import * as fs from "fs"
import { forEachAsync } from "../functions/async-foreach.js";
import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI("AIzaSyDMPxdv2IbifPDLZH6rOguUjcJlP5g4rB8");

let suggestionsIndexMCQ = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];
let totalTokensUsed = 0;
let requestCount = 0;

function filteredSuggestions(suggestions) {
  return suggestions.filter(
    (ele) =>
      !ele.text.includes(
        'Veuillez cocher cette option. De cette façon, vous ne serez pas pénalisé par un point incorrect'
      )
  )
}

function getQuestionCorrectAnswers(suggestions) {
  return suggestions
    .map((ele, index) => ({ index, isCorrect: ele.isCorrect }))
    .filter(ele => ele.isCorrect)
    .map(ele => suggestionsIndexMCQ[ele.index])
}

//Return customized prompt for QCM 
const generateMCQPrompt = (question, module, cours, options, correctAnswers) => {

  const formattedOptions = options
      .map((ele, index) => `${suggestionsIndexMCQ[index]}: ${ele.text}`)
      .join('\n');

      const promptFinalV9 = `
      You are a medical expert in ${module}, helping students understand clinical MCQ questions from the course "${cours}". Your output must be a single block of clean HTML.

// --- CRITICAL RULES --- //
1.  **Language & Tone:** Your output must be in professional, academic French. Do NOT use emojis or conversational filler (e.g., no "Chers étudiants").
2.  **Structure & Order:** Your final output must follow this exact order:
    I.  (Conditional) Avertissement de l'équipe BMSET
    II. Analyse détaillée des propositions
    III. Points Clés & Astuces
    IV. Résumé Visuel
3.  **Formatting:** All text formatting must use HTML tags (e.g., <strong>). Markdown syntax (like **) is strictly forbidden.

// --- CONDITIONAL LOGIC --- //
- **Disputed Answers:** If you are over 95% certain that the provided 'Réponse(s) correcte(s)' is incomplete or incorrect, you MUST start your response with the "Avertissement" block. Provide the official answer and your suggested, more complete answer. Then, proceed with the normal analysis, treating your suggested answer as the correct one for coloring purposes.
- **"Find the False Answer" Questions:** If the question asks to identify the single false proposition, the correct choice is the false statement. For this specific case, you MUST color that entire proposition line GREEN, even though it says "(FAUX)". All other (true) propositions must be colored RED.

// --- HTML STRUCTURE AND STYLES --- //

// (Conditional) Section I: Avertissement de l'équipe BMSET
<div style="background-color: #fff5f5; border-left: 5px solid #e53e3e; padding: 12px 16px; margin-bottom: 24px; border-radius: 4px;">
    <h4 style="font-weight: bold; color: #9b2c2c; margin: 0 0 8px 0;">Avertissement de l'équipe BMSET</h4>
    <p style="margin: 0; color: #c53030; font-size: 15px; line-height: 1.6;">La correction officielle de cette question indique <strong>[Official Answer]</strong>. Toutefois, notre analyse suggère que la réponse la plus complète et exacte serait <strong style="color: #68D391;">[Suggested Answer]</strong>. Cette différence pourrait provenir d’une erreur dans le corrigé officiel ou d’une nuance d’interprétation lors du cours. Nous vous recommandons de vérifier cette question dans votre support de cours et d’en discuter avec vos collègues ou enseignants pour confirmation.</p>
</div>

// Section II: Analyse détaillée des propositions
<h3 style="font-size: 1.25rem; font-weight: bold; color: #1a202c; border-bottom: 2px solid #718096; padding-bottom: 8px; margin-bottom: 16px; margin-top: 16px;">Analyse détaillée des propositions</h3>
// For each proposition, use this exact "card" structure:
<div style="border: 1px solid #e2e8f0; border-radius: 6px; padding: 12px; margin-bottom: 12px;">
    <p style="padding: 8px 12px; border-radius: 4px; margin: 0; font-size: 15px; line-height: 1.6; background-color: [LIGHT_GREEN #e6fffa for VRAI or LIGHT_RED #fff5f5 for FAUX]; color: [DARK_GREEN #234e52 for VRAI or DARK_RED #9b2c2c for FAUX];">
        <strong>[Proposition Text] ([VRAI/FAUX])</strong>
    </p>
    <p style="margin: 12px 0 0 0; font-size: 15px; color: #4a5568; line-height: 1.6;">
        [Explanation with highlighted keywords]
    </p>
</div>
// --- Keyword Highlighting Rules for Analysis:
// - Use strictly two colors for keywords: BOLD BLUE (#2b6cb0) for processes/pathophysiology, and BOLD PURPLE (#6b46c1) for clinical signs/consequences.
// - You may also use BOLD BLACK (#1a202c) for other important terms.
// - Ensure a high density of highlighted keywords in each explanation.

// Section III: Points Clés & Astuces
<div style="background-color: #fefcbf; border-left: 5px solid #f6e05e; padding: 12px 16px; margin-top: 16px;">
    <h4 style="font-weight: bold; color: #975a16; margin: 0 0 8px 0;">Points Clés & Astuces ECN</h4>
    <ul style="margin: 0; padding-left: 0; color: #744210; font-size: 15px; line-height: 1.6; list-style-type: none;">
        <li style="margin-bottom: 8px;"><strong>-</strong> [Bullet point text with highlighted keywords]</li>
    </ul>
</div>
// --- Keyword Highlighting Rules for Points Clés:
// - Colored keywords MUST strictly use BOLD ORANGE (#dd6b20).
// - Other important, non-colored keywords MUST be bolded (<strong>).

// Section IV: Résumé Visuel
<h3 style="font-size: 1.25rem; font-weight: bold; color: #1a202c; border-bottom: 2px solid #718096; padding-bottom: 8px; margin-bottom: 16px; margin-top: 16px;">Résumé Visuel</h3>
// --- INSTRUCTIONS FOR VISUAL SUMMARY LAYOUT --- //
// 1. Analyze the topic. Choose the BEST layout from the three options below.
//    - Use "Layout A" for classifying 2-4 subtypes (e.g., types of thorax).
//    - Use "Layout B" for directly comparing two distinct items (e.g., Stridor vs. Sifflement).
//    - Use "Layout C" as a default for single, linear processes.
// 2. You MUST use one of these layouts. Do not invent a new one.

// --- Layout A: Multi-Panel Mini-Flowcharts --- //
<div style="display: flex; flex-direction: row; flex-wrap: wrap; gap: 16px; margin-top: 24px; justify-content: center;">
    // --- Create a panel for EACH item (e.g., 4 panels for 4 Thorax types) --- //
    <div style="flex: 1 1 300px; border: 1px solid #e2e8f0; border-radius: 8px; padding: 12px; display: flex; flex-direction: column; align-items: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
        <h4 style="font-size: 16px; font-weight: bold; color: #2d3748; margin-bottom: 12px;">[Panel Title]</h4>
        // --- Nodes and Connectors for the mini-flowchart inside the panel --- //
        <div style="border-radius: 4px; padding: 10px 15px; margin-bottom: 8px; text-align: center; font-size: 14px; width: 90%; box-shadow: 0 2px 4px rgba(0,0,0,0.05); background-color: [COLOR]; border: 1px solid [COLOR]; color: [COLOR];">[Node Text]</div>
        <div style="font-size: 20px; color: #4a5568; margin-bottom: 8px;">↓</div>
    </div>
</div>

// --- Layout B: Side-by-Side Comparison Chart --- //
<div style="display: flex; flex-direction: row; flex-wrap: wrap; gap: 16px; margin-top: 24px; justify-content: center;">
    // --- Create a column/panel for EACH of the two items --- //
    <div style="flex: 1 1 300px; background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 12px; display: flex; flex-direction: column; align-items: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
        <h4 style="font-size: 16px; font-weight: bold; color: [TITLE_COLOR]; margin-bottom: 12px;">[COLUMN TITLE]</h4>
        // --- Create a styled info bar for each point. NO ARROWS. --- //
        <div style="border-radius: 6px; padding: 12px; margin-bottom: 10px; text-align: center; font-size: 15px; width: 95%; background-color: [BAR_COLOR]; color: [TEXT_COLOR];">[Info Text]</div>
    </div>
</div>

// --- Layout C: Single-Column Flowchart (for linear processes) --- //
<div style="display: flex; flex-direction: column; align-items: center; margin: 24px 0;">
    // --- Nodes and Connectors for the single flowchart --- //
    <div style="background-color: #e6fffa; border: 1px solid #81e6d9; border-radius: 4px; padding: 10px 15px; margin-bottom: 8px; text-align: center; font-size: 14px; color: #234e52; width: 300px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">[Node Text]</div>
    <div style="font-size: 20px; color: #4a5568; margin-bottom: 8px;">↓</div>
</div>
// --- Keyword Highlighting Rules for Résumé Visuel:
// - Keywords inside flowchart nodes MUST be bolded.
// - You can freely use all established colors (blue, purple, teal, orange, black) for keywords to create a rich visual summary.

---
Question:
${question}

Options:
${formattedOptions}

Réponse(s) correcte(s) : ${correctAnswers.join(', ')}
`;

return promptFinalV9;
};

const generateOpenEndedPrompt = (question, module, cours) => `
Vous êtes un expert médical en ${module} et vous rédigez une réponse modèle à une question clinique ouverte.

Instructions :
- Commencez par un <strong>résumé structuré du cours</strong> "${cours}" comme si vous faisiez une révision rapide à l'étudiant. Incluez définitions, classifications, tableaux, formules, règles cliniques, traitements si besoin.
- Expliquez d'abord les mots-clés médicaux ou scientifiques importants de la question.
- Puis, fournissez une réponse modèle complète avec justification médicale détaillée.
- Formatez toute la réponse en HTML propre et structuré : titres (<h2>, <h3>), paragraphes (<p>), listes (<ul>).
- N'incluez PAS de balises <html>, <head> ou <body>.
- Rédigez en français.

Structure :

<div class="p-3 mb-3">
  <h2 class="text-lg font-bold mb-3">${cours}</h2>
  <p>Résumé du cours</p>
</div>

<div class="p-3 mb-3">
  <h2 class="text-lg font-bold mb-3">Explication des termes médicaux</h2>
  <ul>
    <li><strong>Mot-clé :</strong> définition courte</li>
  </ul>
</div>

<div class="p-3 mb-3">
  <h2 class="text-lg mb-3 font-bold">Réponse typique</h2>
  <p>Réponse détaillée avec justification médicale, analogies et exemples si utile.</p>
</div>

Question :
${question}
`;

async function explainMCQ(question, module, cours, options, correctAnswers) {
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
  const prompt = generateMCQPrompt(question, module, cours, options, correctAnswers);

  const result = await model.generateContent(prompt);
  const response = await result.response;
  
  // Log token usage
  const usageMetadata = result.response.usageMetadata;
  if (usageMetadata) {
    totalTokensUsed += usageMetadata.totalTokenCount;
    console.log('MCQ Token Usage:', {
      promptTokens: usageMetadata.promptTokenCount,
      candidatesTokens: usageMetadata.candidatesTokenCount,
      totalTokens: usageMetadata.totalTokenCount
    });
  }
  
  const html = response.text();
  return { html };
}

async function explainOpen(question, module, cours) {
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
  const prompt = generateOpenEndedPrompt(question, module, cours);

  const result = await model.generateContent(prompt);
  const response = await result.response;
  
  // Log token usage
  const usageMetadata = result.response.usageMetadata;
  if (usageMetadata) {
    totalTokensUsed += usageMetadata.totalTokenCount;
    console.log('Open Question Token Usage:', {
      promptTokens: usageMetadata.promptTokenCount,
      candidatesTokens: usageMetadata.candidatesTokenCount,
      totalTokens: usageMetadata.totalTokenCount
    });
  }
  
  const html = response.text();
  return { html };
}

async function updateQuestionComment(questionData) {
  console.log("updating question ======> ", questionData.id);
  
  // Check if question already has AI-generated explanation
  if (questionData.explanation) {
    const hasAIContent = [
      "Analyse détaillée des propositions",
      "Points Clés & Astuces", 
      "ECN",
      "résumé visuel"
    ].some(keyword => questionData.explanation.includes(keyword));
    
    if (hasAIContent) {
      console.log(`Skipping question ${questionData.id} - already has AI explanation`);
      // return;  // Comment out this line to disable skipping
    }
  }
  
  requestCount++;

  try {
    if (questionData.type !== "qroc" || questionData.type == null) {
      const aiExplanation = await explainMCQ(
        questionData.text,
        questionData.categoryName,
        questionData.subcategoryName,
        filteredSuggestions(questionData.suggestions),
        getQuestionCorrectAnswers(questionData.suggestions)
      );
      updateQuestionV2(questionData.id, 'explanation', aiExplanation.html);
    } else {
      const aiExplanation = await explainOpen(
        questionData.text,
        questionData.categoryName,
        questionData.subcategoryName
      );
      updateQuestionV2(questionData.id, 'explanation', aiExplanation.html);
    }
    
    console.log(`Request ${requestCount} completed. Total tokens used so far: ${totalTokensUsed}`);
  } catch (error) {
    console.log(error);
  }
}

//9hGp3s7MigxO7s7mYidN
//bAGfyf808DlgvjqjypjZ
//gYwLryv4qOYoJyCGAeNb
//ILSqo36AL4XgmJY93CyP
//86bYMtfcF27j7RJOwpCz


//HLtMfEhWQ3unE6I0idvS
//bPT1MnCXqrjDPUwW8VZy
//qEOEAhrKsowKP8gsPCB3
//8Rdj26bv9qm97afpK9EN

//9hGp3s7MigxO7s7mYidN
//bAGfyf808DlgvjqjypjZ
//gYwLryv4qOYoJyCGAeNb
//ILSqo36AL4XgmJY93CyP
//86bYMtfcF27j7RJOwpCz

/*batna cardio 2eme
lDYWgHKy8IGRWvMNqoG2
6qC3udhup1MrhYTZ6BgR
A2vibGDNjqZB7i49SPLo
PvuyMDF4YQawYIoGUsww
*/


(async () => {
  let moduleId = "PvuyMDF4YQawYIoGUsww"
  //let questions = await getQuestionsByModuleId(moduleId)

  let subcatgoryId = "dhOoiziuqvQ8NYexdL66"
  let questions = await getQuestionByCoursId(subcatgoryId)

  //await saveToFileSystem(questions, `${moduleId}.json`)

 // let questionsData = fs.readFileSync(`${moduleId}.json`)
  //let questions = JSON.parse(questionsData)
  await forEachAsync(questions, updateQuestionComment)

 // console.log(questions)
})()
