import { firestore } from "../firebase/config-firebase.js";
import { getQuestionByCoursId, getQuestionsByModuleId, updateQuestionV2 } from "../firebase/questionService.js";
import { saveToFileSystem } from "../functions/save.js";
import * as fs from "fs"
import { forEachAsync } from "../functions/async-foreach.js";
import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI("AIzaSyDMPxdv2IbifPDLZH6rOguUjcJlP5g4rB8");

let suggestionsIndexMCQ = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];
let totalTokensUsed = 0;
let requestCount = 0;

// A map of placeholders to their full inline style counterparts.
const stylePlaceholders = {
    'class="BMSET_WARNING"': 'style="background-color: #fff5f5; border-left: 5px solid #e53e3e; padding: 12px 16px; margin-bottom: 24px; border-radius: 4px;"',
    'class="BMSET_WARNING_H4"': 'style="font-weight: bold; color: #9b2c2c; margin: 0 0 8px 0;"',
    'class="BMSET_WARNING_P"': 'style="margin: 0; color: #c53030; font-size: 15px; line-height: 1.6;"',
    'class="SECTION_TITLE"': 'style="font-size: 1.25rem; font-weight: bold; color: #1a202c; border-bottom: 2px solid #718096; padding-bottom: 8px; margin-bottom: 16px; margin-top: 16px;"',
    'class="ANALYSIS_CARD"': 'style="border: 1px solid #e2e8f0; border-radius: 6px; padding: 12px; margin-bottom: 12px;"',
    'class="PROPOSITION_TEXT_VRAI"': 'style="padding: 8px 12px; border-radius: 4px; margin: 0; font-size: 15px; line-height: 1.6; background-color: #e6fffa; color: #234e52;"',
    'class="PROPOSITION_TEXT_FAUX"': 'style="padding: 8px 12px; border-radius: 4px; margin: 0; font-size: 15px; line-height: 1.6; background-color: #fff5f5; color: #9b2c2c;"',
    'class="EXPLANATION_TEXT"': 'style="margin: 12px 0 0 0; font-size: 15px; color: #4a5568; line-height: 1.6;"',
    'class="POINTS_CLES"': 'style="background-color: #fefcbf; border-left: 5px solid #f6e05e; padding: 12px 16px; margin-top: 16px;"',
    'class="POINTS_CLES_H4"': 'style="font-weight: bold; color: #975a16; margin: 0 0 8px 0;"',
    'class="POINTS_CLES_UL"': 'style="margin: 0; padding-left: 0; color: #744210; font-size: 15px; line-height: 1.6; list-style-type: none;"',
    'class="POINTS_CLES_LI"': 'style="margin-bottom: 8px;"',
    'class="FLEX_CONTAINER"': 'style="display: flex; flex-direction: row; flex-wrap: wrap; gap: 16px; margin-top: 24px; justify-content: center;"',
    'class="FLEX_PANEL"': 'style="flex: 1 1 300px; border: 1px solid #e2e8f0; border-radius: 8px; padding: 12px; display: flex; flex-direction: column; align-items: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05);"',
    'class="FLEX_PANEL_COMPARE"': 'style="flex: 1 1 300px; background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 12px; display: flex; flex-direction: column; align-items: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05);"',
    'class="PANEL_TITLE"': 'style="font-size: 16px; font-weight: bold; color: #2d3748; margin-bottom: 12px;"',
    'class="FLOW_ARROW"': 'style="font-size: 20px; color: #4a5568; margin-bottom: 8px;"',
    'class="FLOW_COLUMN"': 'style="display: flex; flex-direction: column; align-items: center; margin: 24px 0;"',
    'class="NODE_DEFAULT"': 'style="border-radius: 4px; padding: 10px 15px; margin-bottom: 8px; text-align: center; font-size: 14px; width: 300px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); background-color: #e6fffa; border: 1px solid #81e6d9; color: #234e52;"'
};

function filteredSuggestions(suggestions) {
  return suggestions.filter(
    (ele) =>
      !ele.text.includes(
        'Veuillez cocher cette option. De cette façon, vous ne serez pas pénalisé par un point incorrect'
      )
  )
}

function getQuestionCorrectAnswers(suggestions) {
  return suggestions
    .map((ele, index) => ({ index, isCorrect: ele.isCorrect }))
    .filter(ele => ele.isCorrect)
    .map(ele => suggestionsIndexMCQ[ele.index])
}

const getSystemInstructionMCQ = (module, cours) => `
You are a medical expert in ${module}, helping students understand clinical MCQ questions from the course "${cours}".
Your entire output must be a single, clean block of HTML.
Your output must be in professional, academic English.
Do NOT use emojis or conversational filler.
All text formatting must use HTML tags and the placeholder classes provided. Do NOT use inline styles or markdown.
`;

const generateMCQPrompt = (question, module, cours, options, correctAnswers) => {

  const formattedOptions = options
      .map((ele, index) => `${suggestionsIndexMCQ[index]}: ${ele.text}`)
      .join('\n');

      const promptFinalV9 = `
// --- CRITICAL RULES --- //
1.  **Internal Check First:** Before generating any output, you must internally determine the correct answer(s) based on your medical knowledge. Compare your conclusion to the provided 'Correct Answer(s)'. If they do not match exactly (e.g., you find more/fewer/different correct answers), you are REQUIRED to start your response with the "Warning" block.
2.  **Output:** Generate clean HTML using the placeholder classes provided. Do NOT output any inline "style" attributes, except where specified for nodes and bars in the Visual Summary.
3.  **Structure:** If there is no warning, start directly with "Detailed Analysis of Propositions". Otherwise, follow the order: Warning, Detailed Analysis, Key Points & Tips, Visual Summary.
4.  **Keyword Highlighting:** Use <strong> tags with specific color styles for keywords:
    - <strong style="color: #2b6cb0;">TEXT</strong> for processes/pathophysiology.
    - <strong style="color: #6b46c1;">TEXT</strong> for clinical signs/consequences.
    - <strong style="color: #1a202c;">TEXT</strong> for other important terms.
    - <strong style="color: #dd6b20;">TEXT</strong> for keywords in the "Key Points & Tips" section.

// --- HTML STRUCTURE TO FOLLOW (USING PLACEHOLDER CLASSES) --- //

// (Conditional) Section I: Warning
<div class="BMSET_WARNING">
    <h4 class="BMSET_WARNING_H4">BMSET Team Warning</h4>
    <p class="BMSET_WARNING_P">The official answer key indicates <strong>[Official Answer]</strong>. However, our analysis suggests the most complete and accurate answer would be <strong style="color: #68D391;">[Your Suggested Answer]</strong>. [...]</p>
</div>

// Section II: Detailed Analysis of Propositions
<h3 class="SECTION_TITLE">Detailed Analysis of Propositions</h3>
<div class="ANALYSIS_CARD">
    <p class="PROPOSITION_TEXT_VRAI"><strong>[Proposition Text] (TRUE)</strong></p>
    <p class="EXPLANATION_TEXT">[Explanation with highlighted keywords]</p>
</div>
<div class="ANALYSIS_CARD">
    <p class="PROPOSITION_TEXT_FAUX"><strong>[Proposition Text] (FALSE)</strong></p>
    <p class="EXPLANATION_TEXT">[Explanation with highlighted keywords]</p>
</div>

// Section III: Key Points & Tips
<div class="POINTS_CLES">
    <h4 class="POINTS_CLES_H4">Key Points & Exam Tips</h4>
    <ul class="POINTS_CLES_UL">
        <li class="POINTS_CLES_LI"><strong>-</strong> [Bullet point text]</li>
    </ul>
</div>

// Section IV: Visual Summary
<h3 class="SECTION_TITLE">Visual Summary</h3>
// --- INSTRUCTIONS FOR VISUAL SUMMARY LAYOUT --- //
// CRITICAL: You MUST use one of the three layouts below exactly as described. Do not combine them, create new layouts, or deviate from the specified structure. For Layout A and B, you are only permitted to add inline 'style' attributes for colors as specified; do not add any other inline styles.

// --- Layout A: Multi-Panel Mini-Flowcharts (For classifying 2-4 subtypes) --- //
<div class="FLEX_CONTAINER">
    <div class="FLEX_PANEL">
        <h4 class="PANEL_TITLE">[Panel Title]</h4>
        <div style="border-radius: 4px; padding: 10px 15px; margin-bottom: 8px; text-align: center; font-size: 14px; width: 90%; box-shadow: 0 2px 4px rgba(0,0,0,0.05); background-color: [COLOR]; border: 1px solid [COLOR]; color: [COLOR];">[Node Text]</div>
        <div class="FLOW_ARROW">↓</div>
    </div>
</div>

// --- Layout B: Side-by-Side Comparison Chart (For comparing two distinct items) --- //
<div class="FLEX_CONTAINER">
    <div class="FLEX_PANEL_COMPARE">
        <h4 class="PANEL_TITLE" style="color: [TITLE_COLOR];">[COLUMN TITLE]</h4>
        <div style="border-radius: 6px; padding: 12px; margin-bottom: 10px; text-align: center; font-size: 15px; width: 95%; background-color: [BAR_COLOR]; color: [TEXT_COLOR];">[Info Text]</div>
    </div>
</div>

// --- Layout C: Single-Column Flowchart (Default for linear processes) --- //
<div class="FLOW_COLUMN">
    <div class="NODE_DEFAULT">[Node Text]</div>
    <div class="FLOW_ARROW">↓</div>
</div>

---
Question:
${question}

Options:
${formattedOptions}

Correct Answer(s): ${correctAnswers.join(', ')}
`;

return promptFinalV9;
};

const getSystemInstructionOpen = (module, cours) => `
You are a medical expert in ${module}, for the course "${cours}", writing a detailed explanation for an open-ended question.
Your entire output must be a single, clean block of HTML.
Your output must be in professional, academic English.
Do NOT use emojis or conversational filler.
All text formatting must use HTML tags and the placeholder classes provided. Do NOT use inline styles, except where specified in the Visual Summary section.
`;

const generateOpenEndedPrompt = (question, module, cours) => `
// --- CRITICAL RULES --- //
1.  **Output:** Generate clean HTML using the placeholder classes. Do NOT output any inline "style" attributes, except where specified for nodes and bars in the Visual Summary.
2.  **Structure:** Your final output must follow this exact order:
    I. Detailed Answer
    II. Key Points & Tips
    III. Visual Summary
3.  **Keyword Highlighting:** Use <strong> tags with specific color styles for keywords:
    - <strong style="color: #2b6cb0;">TEXT</strong> for processes/pathophysiology.
    - <strong style="color: #6b46c1;">TEXT</strong> for clinical signs/consequences.
    - <strong style="color: #1a202c;">TEXT</strong> for other important terms.
    - <strong style="color: #dd6b20;">TEXT</strong> for keywords in the "Key Points & Tips" section.

// --- HTML STRUCTURE TO FOLLOW (USING PLACEHOLDER CLASSES) --- //

// Section I: Detailed Answer
<h3 class="SECTION_TITLE">Detailed Answer</h3>
<div class="ANALYSIS_CARD">
    <p class="EXPLANATION_TEXT">[Provide a comprehensive, direct answer to the question here. Use highlighted keywords.]</p>
</div>

// Section II: Key Points & Tips
<div class="POINTS_CLES">
    <h4 class="POINTS_CLES_H4">Key Points & Exam Tips</h4>
    <ul class="POINTS_CLES_UL">
        <li class="POINTS_CLES_LI"><strong>-</strong> [Bullet point text with highlighted keywords]</li>
    </ul>
</div>

// Section III: Visual Summary
<h3 class="SECTION_TITLE">Visual Summary</h3>
// --- INSTRUCTIONS FOR VISUAL SUMMARY LAYOUT --- //
// CRITICAL: You MUST use one of the three layouts below exactly as described. Do not combine them, create new layouts, or deviate from the specified structure. For Layout A and B, you are only permitted to add inline 'style' attributes for colors as specified; do not add any other inline styles.

// --- Layout A: Multi-Panel Mini-Flowcharts (For classifying 2-4 subtypes) --- //
<div class="FLEX_CONTAINER">
    <div class="FLEX_PANEL">
        <h4 class="PANEL_TITLE">[Panel Title]</h4>
        <div style="border-radius: 4px; padding: 10px 15px; margin-bottom: 8px; text-align: center; font-size: 14px; width: 90%; box-shadow: 0 2px 4px rgba(0,0,0,0.05); background-color: [COLOR]; border: 1px solid [COLOR]; color: [COLOR];">[Node Text]</div>
        <div class="FLOW_ARROW">↓</div>
    </div>
</div>

// --- Layout B: Side-by-Side Comparison Chart (For comparing two distinct items) --- //
<div class="FLEX_CONTAINER">
    <div class="FLEX_PANEL_COMPARE">
        <h4 class="PANEL_TITLE" style="color: [TITLE_COLOR];">[COLUMN TITLE]</h4>
        <div style="border-radius: 6px; padding: 12px; margin-bottom: 10px; text-align: center; font-size: 15px; width: 95%; background-color: [BAR_COLOR]; color: [TEXT_COLOR];">[Info Text]</div>
    </div>
</div>

// --- Layout C: Single-Column Flowchart (Default for linear processes) --- //
<div class="FLOW_COLUMN">
    <div class="NODE_DEFAULT">[Node Text]</div>
    <div class="FLOW_ARROW">↓</div>
</div>

---
Question:
${question}
`;

async function explainMCQ(question, module, cours, options, correctAnswers) {
  const model = genAI.getGenerativeModel({
    model: "gemini-2.5-flash",
    systemInstruction: getSystemInstructionMCQ(module, cours),
  });
  const prompt = generateMCQPrompt(question, module, cours, options, correctAnswers);

  const result = await model.generateContent(prompt);
  const response = await result.response;
  
  const usageMetadata = result.response.usageMetadata;
  if (usageMetadata) {
    totalTokensUsed += usageMetadata.totalTokenCount;
    console.log('MCQ Token Usage:', {
      promptTokens: usageMetadata.promptTokenCount,
      candidatesTokens: usageMetadata.candidatesTokenCount,
      totalTokens: usageMetadata.totalTokenCount
    });
  }
  
  let html = response.text();

  for (const [placeholder, style] of Object.entries(stylePlaceholders)) {
    const regex = new RegExp(placeholder.replace(/"/g, '\\"'), 'g');
    html = html.replace(regex, style);
  }
  
  return { html };
}

async function explainOpen(question, module, cours) {
  const model = genAI.getGenerativeModel({
    model: "gemini-2.5-flash",
    systemInstruction: getSystemInstructionOpen(module, cours),
  });
  const prompt = generateOpenEndedPrompt(question, module, cours);

  const result = await model.generateContent(prompt);
  const response = await result.response;
  
  const usageMetadata = result.response.usageMetadata;
  if (usageMetadata) {
    totalTokensUsed += usageMetadata.totalTokenCount;
    console.log('Open Question Token Usage:', {
      promptTokens: usageMetadata.promptTokenCount,
      candidatesTokens: usageMetadata.candidatesTokenCount,
      totalTokens: usageMetadata.totalTokenCount
    });
  }
  
  let html = response.text();

  for (const [placeholder, style] of Object.entries(stylePlaceholders)) {
    const regex = new RegExp(placeholder.replace(/"/g, '\\"'), 'g');
    html = html.replace(regex, style);
  }

  return { html };
}

async function updateQuestionComment(questionData) {
  console.log("updating question ======> ", questionData.id);
  
  if (questionData.explanation) {
    const hasAIContent = [
      "Detailed Analysis of Propositions",
      "Key Points & Exam Tips", 
      "Visual Summary",
      "Detailed Answer"
    ].some(keyword => questionData.explanation.includes(keyword));
    
    if (hasAIContent) {
      console.log(`Skipping question ${questionData.id} - already has AI explanation`);
      // return;
    }
  }
  
  requestCount++;

  try {
    if (questionData.type !== "qroc" || questionData.type == null) {
      const aiExplanation = await explainMCQ(
        questionData.text,
        questionData.categoryName,
        questionData.subcategoryName,
        filteredSuggestions(questionData.suggestions),
        getQuestionCorrectAnswers(questionData.suggestions)
      );
      updateQuestionV2(questionData.id, 'explanation', aiExplanation.html);
    } else {
      const aiExplanation = await explainOpen(
        questionData.text,
        questionData.categoryName,
        questionData.subcategoryName
      );
      updateQuestionV2(questionData.id, 'explanation', aiExplanation.html);
    }
    
    console.log(`Request ${requestCount} completed. Total tokens used so far: ${totalTokensUsed}`);
  } catch (error) {
    console.log(error);
  }
}

// Your list of IDs remains untouched
//9hGp3s7MigxO7s7mYidN
//bAGfyf808DlgvjqjypjZ
//gYwLryv4qOYoJyCGAeNb
//ILSqo36AL4XgmJY93CyP
//86bYMtfcF27j7RJOwpCz

(async () => {
  let moduleId = "ak9gWEwACdh1sBJsUcEB"
  let questions = await getQuestionsByModuleId(moduleId)

  let subcatgoryId = "dhOoiziuqvQ8NYexdL66"
  //let questions = await getQuestionByCoursId(subcatgoryId)

  //await saveToFileSystem(questions, `${moduleId}.json`)

 // let questionsData = fs.readFileSync(`${moduleId}.json`)
  //let questions = JSON.parse(questionsData)
  await forEachAsync(questions, updateQuestionComment)

 // console.log(questions)
})()

