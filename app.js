// Global variables to store available options
let availableOptions = {
    locations: [],
    fields: {},
    years: {},
    modules: {},
    subDirs: {}
};

document.addEventListener('DOMContentLoaded', () => {
    // Load available options and current configuration
    loadAvailableOptions().then(() => {
        loadConfig();
        populateCategoryLocationDropdown();
        populateSharedDropdowns();
    });
    loadFirebaseConfig();

    // Initialize the directory browser
    initDirectoryBrowser();

    // Set up event listeners for main actions
    document.getElementById('save-config').addEventListener('click', saveConfig);
    document.getElementById('run-converter').addEventListener('click', runConverter);
    document.getElementById('run-rename').addEventListener('click', runRename);
    document.getElementById('save-firebase-config').addEventListener('click', saveFirebaseConfig);
    document.getElementById('fetch-category').addEventListener('click', fetchCategory);
    document.getElementById('delete-category').addEventListener('click', deleteCategory);
    document.getElementById('delete-questions').addEventListener('click', deleteQuestions);
    document.getElementById('upload-images').addEventListener('click', uploadImages);
    document.getElementById('process-qcm').addEventListener('click', processQCM);
    document.getElementById('browse-image-folder').addEventListener('click', browseImageFolder);

    // Set up event listeners for dropdown changes
    document.getElementById('location').addEventListener('change', handleLocationChange);
    document.getElementById('field').addEventListener('change', handleFieldChange);
    document.getElementById('year').addEventListener('change', handleYearChange);
    document.getElementById('module').addEventListener('change', handleModuleChange);

    // Set up event listeners for shared dropdowns
    document.getElementById('shared-location').addEventListener('change', handleSharedLocationChange);
    document.getElementById('shared-field').addEventListener('change', handleSharedFieldChange);
    document.getElementById('shared-year').addEventListener('change', handleSharedYearChange);
    document.getElementById('shared-module').addEventListener('change', handleSharedModuleChange);
    document.getElementById('shared-subdir').addEventListener('change', handleSharedSubdirChange);

    // Set up event listeners for operation-specific dropdowns
    document.getElementById('image-folder-select').addEventListener('change', handleImageFolderSelectChange);
    document.getElementById('qcm-json-select').addEventListener('change', handleQCMJsonSelectChange);

    // Set up event listeners for image type radio buttons
    document.getElementsByName('image-type').forEach(radio => {
        radio.addEventListener('change', checkImageTypesInFolder);
    });

    // Set up event listeners for tab changes
    document.getElementById('image-tab').addEventListener('click', () => {
        updateImageFolderOptions();
    });
    document.getElementById('qcm-tab').addEventListener('click', () => {
        updateQCMJsonOptions();
    });

    // Set up event listeners for adding new options
    document.getElementById('save-location-btn').addEventListener('click', addNewLocation);
    document.getElementById('save-field-btn').addEventListener('click', addNewField);
    document.getElementById('save-year-btn').addEventListener('click', addNewYear);
    document.getElementById('save-module-btn').addEventListener('click', addNewModule);
    document.getElementById('save-subdir-btn').addEventListener('click', addNewSubDir);

    // Set up smooth scrolling for navigation links
    document.querySelectorAll('.list-group-item').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            targetElement.scrollIntoView({ behavior: 'smooth' });
        });
    });
});

// Populate the category location dropdown
function populateCategoryLocationDropdown() {
    const locationDropdown = document.getElementById('category-location');
    locationDropdown.innerHTML = '<option value="" disabled selected>Select Location</option>';

    // Add locations from the available options
    if (availableOptions && availableOptions.locations) {
        availableOptions.locations.forEach(location => {
            const option = document.createElement('option');
            option.value = location;
            option.textContent = location;
            locationDropdown.appendChild(option);
        });
    }

    // Set default value to match the main location dropdown if available
    const mainLocationDropdown = document.getElementById('location');
    if (mainLocationDropdown.value) {
        // Check if the value exists in the category location dropdown
        const options = Array.from(locationDropdown.options);
        const matchingOption = options.find(option => option.value === mainLocationDropdown.value);
        if (matchingOption) {
            locationDropdown.value = mainLocationDropdown.value;
        }
    }
}

// Load available options from the server
async function loadAvailableOptions() {
    try {
        const response = await fetch('/get-available-options');
        const options = await response.json();

        // Store options globally
        availableOptions = options;

        // Populate location dropdown
        const locationSelect = document.getElementById('location');
        locationSelect.innerHTML = '<option value="" disabled>Select Location</option>';
        options.locations.forEach(location => {
            const option = document.createElement('option');
            option.value = location;
            option.textContent = location;
            locationSelect.appendChild(option);
        });

        return options;
    } catch (error) {
        console.error('Error loading available options:', error);
    }
}

// Load current configuration
async function loadConfig() {
    try {
        const response = await fetch('/get-config');
        const config = await response.json();

        // Set form values for dropdowns
        const locationSelect = document.getElementById('location');
        if (config.location) {
            // If the location exists in the options, select it
            if (Array.from(locationSelect.options).some(option => option.value === config.location)) {
                locationSelect.value = config.location;
                // Trigger the change event to populate dependent dropdowns
                locationSelect.dispatchEvent(new Event('change'));

                // After location change event, set field value if it exists
                setTimeout(() => {
                    const fieldSelect = document.getElementById('field');
                    if (config.field && Array.from(fieldSelect.options).some(option => option.value === config.field)) {
                        fieldSelect.value = config.field;
                        fieldSelect.dispatchEvent(new Event('change'));

                        // After field change event, set year value if it exists
                        setTimeout(() => {
                            const yearSelect = document.getElementById('year');
                            if (config.year && Array.from(yearSelect.options).some(option => option.value === config.year.toString())) {
                                yearSelect.value = config.year.toString();
                                yearSelect.dispatchEvent(new Event('change'));

                                // After year change event, set module value if it exists
                                setTimeout(() => {
                                    const moduleSelect = document.getElementById('module');
                                    if (config.module && Array.from(moduleSelect.options).some(option => option.value === config.module)) {
                                        moduleSelect.value = config.module;
                                        moduleSelect.dispatchEvent(new Event('change'));

                                        // After module change event, set subDirQcm value if it exists
                                        setTimeout(() => {
                                            const subDirSelect = document.getElementById('subDirQcm');
                                            if (config.subDirQcm && Array.from(subDirSelect.options).some(option => option.value === config.subDirQcm)) {
                                                subDirSelect.value = config.subDirQcm;
                                            }
                                        }, 100);
                                    }
                                }, 100);
                            }
                        }, 100);
                    }
                }, 100);
            }
        }

        // Set other form values
        document.getElementById('filesType').value = config.filesType;
        document.getElementById('test').checked = config.test;
        document.getElementById('withChapter').checked = config.withChapter;
        document.getElementById('oneCommentFile').checked = config.oneCommentFile;
        document.getElementById('useCommentIndex').checked = config.useCommentIndex;
        document.getElementById('useCommentOrder').checked = config.useCommentOrder;
        document.getElementById('upload').checked = config.upload;
        document.getElementById('update').checked = config.update;
        document.getElementById('source').value = config.source;
    } catch (error) {
        console.error('Error loading configuration:', error);
    }
}

// Handle location dropdown change
function handleLocationChange() {
    const locationSelect = document.getElementById('location');
    const fieldSelect = document.getElementById('field');
    const selectedLocation = locationSelect.value;

    // Clear dependent dropdowns
    fieldSelect.innerHTML = '<option value="" disabled selected>Select Field</option>';
    document.getElementById('year').innerHTML = '<option value="" disabled selected>Select Year</option>';
    document.getElementById('module').innerHTML = '<option value="" disabled selected>Select Module</option>';
    document.getElementById('subDirQcm').innerHTML = '<option value="" disabled selected>Select Sub Directory</option>';

    // Populate field dropdown based on selected location
    if (selectedLocation && availableOptions.fields[selectedLocation]) {
        availableOptions.fields[selectedLocation].forEach(field => {
            const option = document.createElement('option');
            option.value = field;
            option.textContent = field;
            fieldSelect.appendChild(option);
        });
    }
}

// Handle field dropdown change
function handleFieldChange() {
    const locationSelect = document.getElementById('location');
    const fieldSelect = document.getElementById('field');
    const yearSelect = document.getElementById('year');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;

    // Clear dependent dropdowns
    yearSelect.innerHTML = '<option value="" disabled selected>Select Year</option>';
    document.getElementById('module').innerHTML = '<option value="" disabled selected>Select Module</option>';
    document.getElementById('subDirQcm').innerHTML = '<option value="" disabled selected>Select Sub Directory</option>';

    // Populate year dropdown based on selected location and field
    if (selectedLocation && selectedField &&
        availableOptions.years[selectedLocation] &&
        availableOptions.years[selectedLocation][selectedField]) {

        availableOptions.years[selectedLocation][selectedField].forEach(year => {
            const option = document.createElement('option');
            option.value = year.toString();
            option.textContent = year.toString();
            yearSelect.appendChild(option);
        });
    }
}

// Handle year dropdown change
function handleYearChange() {
    const locationSelect = document.getElementById('location');
    const fieldSelect = document.getElementById('field');
    const yearSelect = document.getElementById('year');
    const moduleSelect = document.getElementById('module');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = parseInt(yearSelect.value);

    // Clear dependent dropdowns
    moduleSelect.innerHTML = '<option value="" disabled selected>Select Module</option>';
    document.getElementById('subDirQcm').innerHTML = '<option value="" disabled selected>Select Sub Directory</option>';

    // Populate module dropdown based on selected location, field, and year
    if (selectedLocation && selectedField && selectedYear &&
        availableOptions.modules[selectedLocation] &&
        availableOptions.modules[selectedLocation][selectedField] &&
        availableOptions.modules[selectedLocation][selectedField][selectedYear]) {

        availableOptions.modules[selectedLocation][selectedField][selectedYear].forEach(module => {
            const option = document.createElement('option');
            option.value = module;
            option.textContent = module;
            moduleSelect.appendChild(option);
        });
    }
}

// Handle module dropdown change
function handleModuleChange() {
    const locationSelect = document.getElementById('location');
    const fieldSelect = document.getElementById('field');
    const yearSelect = document.getElementById('year');
    const moduleSelect = document.getElementById('module');
    const subDirSelect = document.getElementById('subDirQcm');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = parseInt(yearSelect.value);
    const selectedModule = moduleSelect.value;

    // Clear sub directory dropdown
    subDirSelect.innerHTML = '<option value="" disabled selected>Select Sub Directory</option>';

    // Populate sub directory dropdown based on selected location, field, year, and module
    if (selectedLocation && selectedField && selectedYear && selectedModule &&
        availableOptions.subDirs[selectedLocation] &&
        availableOptions.subDirs[selectedLocation][selectedField] &&
        availableOptions.subDirs[selectedLocation][selectedField][selectedYear] &&
        availableOptions.subDirs[selectedLocation][selectedField][selectedYear][selectedModule]) {

        availableOptions.subDirs[selectedLocation][selectedField][selectedYear][selectedModule].forEach(subDir => {
            const option = document.createElement('option');
            option.value = subDir;
            option.textContent = subDir;
            subDirSelect.appendChild(option);
        });
    }
}

// Load Firebase configuration
async function loadFirebaseConfig() {
    try {
        const response = await fetch('/get-firebase-config');
        const config = await response.json();

        document.getElementById('useEmulator').checked = config.useEmulator;
    } catch (error) {
        console.error('Error loading Firebase configuration:', error);
    }
}

// Add new location
async function addNewLocation() {
    const newLocationInput = document.getElementById('new-location');
    const newLocation = newLocationInput.value.trim().toUpperCase();

    if (!newLocation) {
        alert('Please enter a location name');
        return;
    }

    // Create the directory structure
    try {
        // Create the base directory for the new location
        const locationPath = `${newLocation}`;

        // Add the new location to the dropdown
        const locationSelect = document.getElementById('location');
        const option = document.createElement('option');
        option.value = newLocation;
        option.textContent = newLocation;
        locationSelect.appendChild(option);

        // Select the new location
        locationSelect.value = newLocation;
        locationSelect.dispatchEvent(new Event('change'));

        // Add the new location to the available options
        availableOptions.locations.push(newLocation);
        availableOptions.fields[newLocation] = [];

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addLocationModal'));
        modal.hide();

        // Clear the input
        newLocationInput.value = '';

        // Reload available options to reflect the changes
        await loadAvailableOptions();

        alert(`Location ${newLocation} added successfully!`);
    } catch (error) {
        console.error('Error adding new location:', error);
        alert('Error adding new location: ' + error.message);
    }
}

// Add new field
async function addNewField() {
    const locationSelect = document.getElementById('location');
    const newFieldInput = document.getElementById('new-field');
    const selectedLocation = locationSelect.value;
    const newField = newFieldInput.value.trim().toUpperCase();

    if (!selectedLocation) {
        alert('Please select a location first');
        return;
    }

    if (!newField) {
        alert('Please enter a field name');
        return;
    }

    // Create the directory structure
    try {
        // Create the base directory for the new field
        const fieldPath = `${selectedLocation}/${newField}`;

        // Add the new field to the dropdown
        const fieldSelect = document.getElementById('field');
        const option = document.createElement('option');
        option.value = newField;
        option.textContent = newField;
        fieldSelect.appendChild(option);

        // Select the new field
        fieldSelect.value = newField;
        fieldSelect.dispatchEvent(new Event('change'));

        // Add the new field to the available options
        if (!availableOptions.fields[selectedLocation]) {
            availableOptions.fields[selectedLocation] = [];
        }
        availableOptions.fields[selectedLocation].push(newField);
        availableOptions.years[selectedLocation][newField] = [];

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addFieldModal'));
        modal.hide();

        // Clear the input
        newFieldInput.value = '';

        // Reload available options to reflect the changes
        await loadAvailableOptions();

        alert(`Field ${newField} added successfully!`);
    } catch (error) {
        console.error('Error adding new field:', error);
        alert('Error adding new field: ' + error.message);
    }
}

// Track pending changes that need to be saved
let pendingChanges = {
    newYears: {},
    newModules: {},
    newSubDirs: {}
};

// Add new year
async function addNewYear() {
    const locationSelect = document.getElementById('location');
    const fieldSelect = document.getElementById('field');
    const newYearInput = document.getElementById('new-year');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const newYear = parseInt(newYearInput.value.trim());

    if (!selectedLocation) {
        alert('Please select a location first');
        return;
    }

    if (!selectedField) {
        alert('Please select a field first');
        return;
    }

    if (isNaN(newYear) || newYear < 1 || newYear > 10) {
        alert('Please enter a valid year (1-10)');
        return;
    }

    try {
        // Define the paths for the new year
        const yearPath = `${selectedLocation}/${selectedField}/${newYear}-year`;
        const qcmPath = `${yearPath}/qcm`;
        const commentsPath = `${yearPath}/comments`;

        // Add the new year to the dropdown
        const yearSelect = document.getElementById('year');
        const option = document.createElement('option');
        option.value = newYear.toString();
        option.textContent = newYear.toString() + ' (unsaved)';
        option.classList.add('text-warning');
        yearSelect.appendChild(option);

        // Select the new year
        yearSelect.value = newYear.toString();
        yearSelect.dispatchEvent(new Event('change'));

        // Add the new year to the available options locally
        if (!availableOptions.years[selectedLocation]) {
            availableOptions.years[selectedLocation] = {};
        }
        if (!availableOptions.years[selectedLocation][selectedField]) {
            availableOptions.years[selectedLocation][selectedField] = [];
        }
        availableOptions.years[selectedLocation][selectedField].push(newYear);
        if (!availableOptions.modules[selectedLocation]) {
            availableOptions.modules[selectedLocation] = {};
        }
        if (!availableOptions.modules[selectedLocation][selectedField]) {
            availableOptions.modules[selectedLocation][selectedField] = {};
        }
        availableOptions.modules[selectedLocation][selectedField][newYear] = [];

        // Track this as a pending change
        if (!pendingChanges.newYears[selectedLocation]) {
            pendingChanges.newYears[selectedLocation] = {};
        }
        if (!pendingChanges.newYears[selectedLocation][selectedField]) {
            pendingChanges.newYears[selectedLocation][selectedField] = [];
        }
        pendingChanges.newYears[selectedLocation][selectedField].push({
            year: newYear,
            yearPath: yearPath,
            qcmPath: qcmPath,
            commentsPath: commentsPath
        });

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addYearModal'));
        modal.hide();

        // Clear the input
        newYearInput.value = '';

        // Show a notification to save changes
        const saveBtn = document.getElementById('save-config');
        saveBtn.classList.add('btn-warning');
        saveBtn.classList.remove('btn-primary');
        saveBtn.innerHTML = '<i class="bi bi-save"></i> Save Config (Unsaved Changes)';

        alert(`Year ${newYear} added successfully! Click 'Save Config' to permanently save this change.`);
    } catch (error) {
        console.error('Error adding new year:', error);
        alert('Error adding new year: ' + error.message);
    }
}

// Add new module
async function addNewModule() {
    const locationSelect = document.getElementById('location');
    const fieldSelect = document.getElementById('field');
    const yearSelect = document.getElementById('year');
    const newModuleInput = document.getElementById('new-module');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = parseInt(yearSelect.value);
    const newModule = newModuleInput.value.trim().toUpperCase();

    if (!selectedLocation) {
        alert('Please select a location first');
        return;
    }

    if (!selectedField) {
        alert('Please select a field first');
        return;
    }

    if (isNaN(selectedYear)) {
        alert('Please select a year first');
        return;
    }

    if (!newModule) {
        alert('Please enter a module name');
        return;
    }

    try {
        // Define the paths for the new module
        const modulePath = `${selectedLocation}/${selectedField}/${selectedYear}-year/qcm/${newModule}`;
        const commentsPath = `${selectedLocation}/${selectedField}/${selectedYear}-year/comments/${newModule}`;

        // Add the new module to the dropdown
        const moduleSelect = document.getElementById('module');
        const option = document.createElement('option');
        option.value = newModule;
        option.textContent = newModule + ' (unsaved)';
        option.classList.add('text-warning');
        moduleSelect.appendChild(option);

        // Select the new module
        moduleSelect.value = newModule;
        moduleSelect.dispatchEvent(new Event('change'));

        // Add the new module to the available options locally
        if (!availableOptions.modules[selectedLocation]) {
            availableOptions.modules[selectedLocation] = {};
        }
        if (!availableOptions.modules[selectedLocation][selectedField]) {
            availableOptions.modules[selectedLocation][selectedField] = {};
        }
        if (!availableOptions.modules[selectedLocation][selectedField][selectedYear]) {
            availableOptions.modules[selectedLocation][selectedField][selectedYear] = [];
        }
        availableOptions.modules[selectedLocation][selectedField][selectedYear].push(newModule);

        // Initialize the subDirs for this module
        if (!availableOptions.subDirs[selectedLocation]) {
            availableOptions.subDirs[selectedLocation] = {};
        }
        if (!availableOptions.subDirs[selectedLocation][selectedField]) {
            availableOptions.subDirs[selectedLocation][selectedField] = {};
        }
        if (!availableOptions.subDirs[selectedLocation][selectedField][selectedYear]) {
            availableOptions.subDirs[selectedLocation][selectedField][selectedYear] = {};
        }
        availableOptions.subDirs[selectedLocation][selectedField][selectedYear][newModule] = [];

        // Track this as a pending change
        if (!pendingChanges.newModules[selectedLocation]) {
            pendingChanges.newModules[selectedLocation] = {};
        }
        if (!pendingChanges.newModules[selectedLocation][selectedField]) {
            pendingChanges.newModules[selectedLocation][selectedField] = {};
        }
        if (!pendingChanges.newModules[selectedLocation][selectedField][selectedYear]) {
            pendingChanges.newModules[selectedLocation][selectedField][selectedYear] = [];
        }
        pendingChanges.newModules[selectedLocation][selectedField][selectedYear].push({
            module: newModule,
            modulePath: modulePath,
            commentsPath: commentsPath
        });

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addModuleModal'));
        modal.hide();

        // Clear the input
        newModuleInput.value = '';

        // Show a notification to save changes
        const saveBtn = document.getElementById('save-config');
        saveBtn.classList.add('btn-warning');
        saveBtn.classList.remove('btn-primary');
        saveBtn.innerHTML = '<i class="bi bi-save"></i> Save Config (Unsaved Changes)';

        alert(`Module ${newModule} added successfully! Click 'Save Config' to permanently save this change.`);
    } catch (error) {
        console.error('Error adding new module:', error);
        alert('Error adding new module: ' + error.message);
    }
}

// Add new sub directory
async function addNewSubDir() {
    const locationSelect = document.getElementById('location');
    const fieldSelect = document.getElementById('field');
    const yearSelect = document.getElementById('year');
    const moduleSelect = document.getElementById('module');
    const newSubDirInput = document.getElementById('new-subdir');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = parseInt(yearSelect.value);
    const selectedModule = moduleSelect.value;
    const newSubDir = newSubDirInput.value.trim().toUpperCase();

    if (!selectedLocation) {
        alert('Please select a location first');
        return;
    }

    if (!selectedField) {
        alert('Please select a field first');
        return;
    }

    if (isNaN(selectedYear)) {
        alert('Please select a year first');
        return;
    }

    if (!selectedModule) {
        alert('Please select a module first');
        return;
    }

    if (!newSubDir) {
        alert('Please enter a sub directory name');
        return;
    }

    try {
        // Define the paths for the new sub directory
        const subDirPath = `${selectedLocation}/${selectedField}/${selectedYear}-year/qcm/${selectedModule}/${newSubDir}`;
        const jsonSubDirPath = `${selectedLocation}/${selectedField}/${selectedYear}-year/qcm/${selectedModule}/JSON_${newSubDir}`;

        // Add the new sub directory to the dropdown
        const subDirSelect = document.getElementById('subDirQcm');
        const option = document.createElement('option');
        option.value = newSubDir;
        option.textContent = newSubDir + ' (unsaved)';
        option.classList.add('text-warning');
        subDirSelect.appendChild(option);

        // Select the new sub directory
        subDirSelect.value = newSubDir;

        // Add the new sub directory to the available options locally
        if (!availableOptions.subDirs[selectedLocation]) {
            availableOptions.subDirs[selectedLocation] = {};
        }
        if (!availableOptions.subDirs[selectedLocation][selectedField]) {
            availableOptions.subDirs[selectedLocation][selectedField] = {};
        }
        if (!availableOptions.subDirs[selectedLocation][selectedField][selectedYear]) {
            availableOptions.subDirs[selectedLocation][selectedField][selectedYear] = {};
        }
        if (!availableOptions.subDirs[selectedLocation][selectedField][selectedYear][selectedModule]) {
            availableOptions.subDirs[selectedLocation][selectedField][selectedYear][selectedModule] = [];
        }
        availableOptions.subDirs[selectedLocation][selectedField][selectedYear][selectedModule].push(newSubDir);

        // Track this as a pending change
        if (!pendingChanges.newSubDirs[selectedLocation]) {
            pendingChanges.newSubDirs[selectedLocation] = {};
        }
        if (!pendingChanges.newSubDirs[selectedLocation][selectedField]) {
            pendingChanges.newSubDirs[selectedLocation][selectedField] = {};
        }
        if (!pendingChanges.newSubDirs[selectedLocation][selectedField][selectedYear]) {
            pendingChanges.newSubDirs[selectedLocation][selectedField][selectedYear] = {};
        }
        if (!pendingChanges.newSubDirs[selectedLocation][selectedField][selectedYear][selectedModule]) {
            pendingChanges.newSubDirs[selectedLocation][selectedField][selectedYear][selectedModule] = [];
        }
        pendingChanges.newSubDirs[selectedLocation][selectedField][selectedYear][selectedModule].push({
            subDir: newSubDir,
            subDirPath: subDirPath,
            jsonSubDirPath: jsonSubDirPath
        });

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addSubDirModal'));
        modal.hide();

        // Clear the input
        newSubDirInput.value = '';

        // Show a notification to save changes
        const saveBtn = document.getElementById('save-config');
        saveBtn.classList.add('btn-warning');
        saveBtn.classList.remove('btn-primary');
        saveBtn.innerHTML = '<i class="bi bi-save"></i> Save Config (Unsaved Changes)';

        alert(`Sub directory ${newSubDir} added successfully! Click 'Save Config' to permanently save this change.`);
    } catch (error) {
        console.error('Error adding new sub directory:', error);
        alert('Error adding new sub directory: ' + error.message);
    }
}

// Save configuration
async function saveConfig() {
    const config = {
        location: document.getElementById('location').value,
        field: document.getElementById('field').value,
        module: document.getElementById('module').value,
        year: parseInt(document.getElementById('year').value),
        subDirQcm: document.getElementById('subDirQcm').value,
        filesType: document.getElementById('filesType').value,
        test: document.getElementById('test').checked,
        withChapter: document.getElementById('withChapter').checked,
        oneCommentFile: document.getElementById('oneCommentFile').checked,
        useCommentIndex: document.getElementById('useCommentIndex').checked,
        useCommentOrder: document.getElementById('useCommentOrder').checked,
        upload: document.getElementById('upload').checked,
        update: document.getElementById('update').checked,
        source: document.getElementById('source').value
    };

    try {
        // First, process any pending changes
        let createdDirectories = [];

        // Process pending new years
        if (Object.keys(pendingChanges.newYears).length > 0) {
            for (const location in pendingChanges.newYears) {
                for (const field in pendingChanges.newYears[location]) {
                    for (const yearData of pendingChanges.newYears[location][field]) {
                        // Create the directories on the server
                        await fetch('/create-directory', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ dirPath: yearData.yearPath })
                        });
                        await fetch('/create-directory', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ dirPath: yearData.qcmPath })
                        });
                        await fetch('/create-directory', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ dirPath: yearData.commentsPath })
                        });

                        createdDirectories.push({
                            type: 'year',
                            yearPath: yearData.yearPath,
                            qcmPath: yearData.qcmPath,
                            commentsPath: yearData.commentsPath
                        });
                    }
                }
            }
        }

        // Process pending new modules
        if (Object.keys(pendingChanges.newModules).length > 0) {
            for (const location in pendingChanges.newModules) {
                for (const field in pendingChanges.newModules[location]) {
                    for (const year in pendingChanges.newModules[location][field]) {
                        for (const moduleData of pendingChanges.newModules[location][field][year]) {
                            // Create the directories on the server
                            await fetch('/create-directory', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ dirPath: moduleData.modulePath })
                            });
                            await fetch('/create-directory', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ dirPath: moduleData.commentsPath })
                            });

                            createdDirectories.push({
                                type: 'module',
                                module: moduleData.module,
                                modulePath: moduleData.modulePath,
                                commentsPath: moduleData.commentsPath
                            });
                        }
                    }
                }
            }
        }

        // Process pending new subdirectories
        if (Object.keys(pendingChanges.newSubDirs).length > 0) {
            for (const location in pendingChanges.newSubDirs) {
                for (const field in pendingChanges.newSubDirs[location]) {
                    for (const year in pendingChanges.newSubDirs[location][field]) {
                        for (const module in pendingChanges.newSubDirs[location][field][year]) {
                            for (const subDirData of pendingChanges.newSubDirs[location][field][year][module]) {
                                // Create the directories on the server
                                await fetch('/create-directory', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({ dirPath: subDirData.subDirPath })
                                });
                                await fetch('/create-directory', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({ dirPath: subDirData.jsonSubDirPath })
                                });

                                createdDirectories.push({
                                    type: 'subDir',
                                    subDir: subDirData.subDir,
                                    subDirPath: subDirData.subDirPath,
                                    jsonSubDirPath: subDirData.jsonSubDirPath
                                });
                            }
                        }
                    }
                }
            }
        }

        // Save the configuration
        const response = await fetch('/save-config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });

        const result = await response.json();

        if (result.success) {
            // Update any year options that were marked as unsaved
            const yearSelect = document.getElementById('year');
            Array.from(yearSelect.options).forEach(option => {
                if (option.textContent.includes('(unsaved)')) {
                    option.textContent = option.value;
                    option.classList.remove('text-warning');
                }
            });

            // Update any module options that were marked as unsaved
            const moduleSelect = document.getElementById('module');
            Array.from(moduleSelect.options).forEach(option => {
                if (option.textContent.includes('(unsaved)')) {
                    option.textContent = option.value;
                    option.classList.remove('text-warning');
                }
            });

            // Update any subDir options that were marked as unsaved
            const subDirSelect = document.getElementById('subDirQcm');
            Array.from(subDirSelect.options).forEach(option => {
                if (option.textContent.includes('(unsaved)')) {
                    option.textContent = option.value;
                    option.classList.remove('text-warning');
                }
            });

            // Reset the save button
            const saveBtn = document.getElementById('save-config');
            saveBtn.classList.remove('btn-warning');
            saveBtn.classList.add('btn-primary');
            saveBtn.innerHTML = '<i class="bi bi-save"></i> Save Config';

            // Show success message
            alert('Configuration saved successfully!');

            // Clear pending changes
            pendingChanges = {
                newYears: {},
                newModules: {},
                newSubDirs: {}
            };

            // Create a modal to display the paths
            let additionalDirectoriesHtml = '';
            if (createdDirectories.length > 0) {
                // Group directories by type
                const yearDirs = createdDirectories.filter(dir => dir.type === 'year');
                const moduleDirs = createdDirectories.filter(dir => dir.type === 'module');
                const subDirDirs = createdDirectories.filter(dir => dir.type === 'subDir');

                let directoriesHtml = '';

                // Add year directories
                if (yearDirs.length > 0) {
                    directoriesHtml += `
                        <div class="mb-3">
                            <h6>Years:</h6>
                            <ul class="list-group">
                                ${yearDirs.map(dir => `
                                    <li class="list-group-item">
                                        <strong>Year Path:</strong> ${dir.yearPath}<br>
                                        <strong>QCM Path:</strong> ${dir.qcmPath}<br>
                                        <strong>Comments Path:</strong> ${dir.commentsPath}
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    `;
                }

                // Add module directories
                if (moduleDirs.length > 0) {
                    directoriesHtml += `
                        <div class="mb-3">
                            <h6>Modules:</h6>
                            <ul class="list-group">
                                ${moduleDirs.map(dir => `
                                    <li class="list-group-item">
                                        <strong>Module:</strong> ${dir.module}<br>
                                        <strong>Module Path:</strong> ${dir.modulePath}<br>
                                        <strong>Comments Path:</strong> ${dir.commentsPath}
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    `;
                }

                // Add subdirectory directories
                if (subDirDirs.length > 0) {
                    directoriesHtml += `
                        <div class="mb-3">
                            <h6>Sub Directories:</h6>
                            <ul class="list-group">
                                ${subDirDirs.map(dir => `
                                    <li class="list-group-item">
                                        <strong>Sub Directory:</strong> ${dir.subDir}<br>
                                        <strong>Path:</strong> ${dir.subDirPath}<br>
                                        <strong>JSON Path:</strong> ${dir.jsonSubDirPath}
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    `;
                }

                additionalDirectoriesHtml = `
                    <div class="mt-4">
                        <h6>Additional directories created:</h6>
                        ${directoriesHtml}
                    </div>
                `;
            }

            const pathsHtml = `
                <div class="modal fade" id="pathsModal" tabindex="-1" aria-labelledby="pathsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="pathsModalLabel">Configuration Saved Successfully</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>The following directories have been created:</p>
                                <div class="mb-3">
                                    <strong>Comments Folder:</strong>
                                    <pre class="bg-light p-2">${result.paths.commentFolder}</pre>
                                </div>
                                <div class="mb-3">
                                    <strong>QCM Folder:</strong>
                                    <pre class="bg-light p-2">${result.paths.qcmFolder}</pre>
                                </div>
                                <div class="mb-3">
                                    <strong>JSON QCM Folder:</strong>
                                    <pre class="bg-light p-2">${result.paths.jsonQcmFolder}</pre>
                                </div>
                                ${additionalDirectoriesHtml}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add the modal to the document if it doesn't exist
            if (!document.getElementById('pathsModal')) {
                const modalDiv = document.createElement('div');
                modalDiv.innerHTML = pathsHtml;
                document.body.appendChild(modalDiv.firstElementChild);
            } else {
                // Update the existing modal
                document.querySelector('#pathsModal .modal-body').innerHTML = `
                    <p>The following directories have been created:</p>
                    <div class="mb-3">
                        <strong>Comments Folder:</strong>
                        <pre class="bg-light p-2">${result.paths.commentFolder}</pre>
                    </div>
                    <div class="mb-3">
                        <strong>QCM Folder:</strong>
                        <pre class="bg-light p-2">${result.paths.qcmFolder}</pre>
                    </div>
                    <div class="mb-3">
                        <strong>JSON QCM Folder:</strong>
                        <pre class="bg-light p-2">${result.paths.jsonQcmFolder}</pre>
                    </div>
                    ${additionalDirectoriesHtml}
                `;
            }

            // Show the modal
            const pathsModal = new bootstrap.Modal(document.getElementById('pathsModal'));
            pathsModal.show();

            // Store current selections before reloading options
            const currentSelections = {
                location: document.getElementById('location').value,
                field: document.getElementById('field').value,
                year: document.getElementById('year').value,
                module: document.getElementById('module').value,
                subDirQcm: document.getElementById('subDirQcm').value
            };

            // Reload available options to reflect the changes
            await loadAvailableOptions();

            // Restore the current selections
            const locationSelect = document.getElementById('location');
            if (currentSelections.location && Array.from(locationSelect.options).some(option => option.value === currentSelections.location)) {
                locationSelect.value = currentSelections.location;
                locationSelect.dispatchEvent(new Event('change'));

                // After location change event, restore field
                setTimeout(() => {
                    const fieldSelect = document.getElementById('field');
                    if (currentSelections.field && Array.from(fieldSelect.options).some(option => option.value === currentSelections.field)) {
                        fieldSelect.value = currentSelections.field;
                        fieldSelect.dispatchEvent(new Event('change'));

                        // After field change event, restore year
                        setTimeout(() => {
                            const yearSelect = document.getElementById('year');
                            if (currentSelections.year && Array.from(yearSelect.options).some(option => option.value === currentSelections.year)) {
                                yearSelect.value = currentSelections.year;
                                yearSelect.dispatchEvent(new Event('change'));

                                // After year change event, restore module
                                setTimeout(() => {
                                    const moduleSelect = document.getElementById('module');
                                    if (currentSelections.module && Array.from(moduleSelect.options).some(option => option.value === currentSelections.module)) {
                                        moduleSelect.value = currentSelections.module;
                                        moduleSelect.dispatchEvent(new Event('change'));

                                        // After module change event, restore subDirQcm
                                        setTimeout(() => {
                                            const subDirSelect = document.getElementById('subDirQcm');
                                            if (currentSelections.subDirQcm && Array.from(subDirSelect.options).some(option => option.value === currentSelections.subDirQcm)) {
                                                subDirSelect.value = currentSelections.subDirQcm;
                                            }
                                        }, 100);
                                    }
                                }, 100);
                            }
                        }, 100);
                    }
                }, 100);
            }
        } else {
            console.error('Server error:', result);
            alert('Error saving configuration: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error saving configuration:', error);
        alert('Error saving configuration: ' + (error.message || 'Unknown error'));
    }
}

// Run docxToJsonConverter.js
async function runConverter() {
    try {
        const response = await fetch('/run-converter', {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            alert('Converter executed successfully!');
            console.log(result.output);
        } else {
            console.error('Server error:', result);
            alert('Error running converter: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error running converter:', error);
        alert('Error running converter: ' + (error.message || 'Unknown error'));
    }
}

// Directory browser variables
let currentDirectoryPath = '.';
let directoryBrowserModal;

// Initialize directory browser
function initDirectoryBrowser() {
    directoryBrowserModal = new bootstrap.Modal(document.getElementById('directoryBrowserModal'));

    // Set up event listeners for directory browser
    document.getElementById('browse-directory').addEventListener('click', openDirectoryBrowser);
    document.getElementById('select-directory').addEventListener('click', selectCurrentDirectory);
    document.getElementById('directory-breadcrumb').addEventListener('click', handleBreadcrumbClick);
    document.getElementById('directory-list').addEventListener('click', handleDirectoryListClick);
}

// Open the directory browser modal
async function openDirectoryBrowser() {
    // Reset to root directory
    currentDirectoryPath = '.';
    await loadDirectoryContents(currentDirectoryPath);
    directoryBrowserModal.show();
}

// Load directory contents
async function loadDirectoryContents(dirPath) {
    try {
        const response = await fetch(`/browse-directory?path=${encodeURIComponent(dirPath)}`);
        const data = await response.json();

        if (response.ok) {
            // Update current directory path
            currentDirectoryPath = data.relativePath;

            // Update breadcrumb
            updateBreadcrumb(data);

            // Update directory list
            updateDirectoryList(data);

            // Update directory info
            document.getElementById('directory-info').innerHTML = `
                <span class="badge bg-info">${data.docxCount} .docx files</span>
                <span class="badge bg-success ms-1">${data.imageCount} images</span>
                ${data.pngCount > 0 ? `<span class="badge bg-primary ms-1">${data.pngCount} PNG</span>` : ''}
                ${data.jpgCount > 0 ? `<span class="badge bg-secondary ms-1">${data.jpgCount} JPG</span>` : ''}
                <span class="text-muted ms-2">${data.currentPath}</span>
            `;

            // If we're in the image tab and auto-detect is selected, update the detected images info
            if (document.getElementById('image-tab').classList.contains('active') &&
                document.getElementById('image-type-auto').checked) {
                const infoElement = document.getElementById('detected-images-info');
                if (data.imageCount > 0) {
                    infoElement.textContent = `Detected ${data.imageCount} images: ${data.pngCount} PNG, ${data.jpgCount} JPG`;

                    if (data.pngCount > data.jpgCount) {
                        infoElement.textContent += " (Will use PNG)";
                    } else if (data.jpgCount > 0) {
                        infoElement.textContent += " (Will use JPG)";
                    } else {
                        infoElement.textContent += " (Will use PNG by default)";
                    }
                } else {
                    infoElement.textContent = 'No images detected in the selected folder';
                }
            }
        } else {
            console.error('Error loading directory:', data.error);
            alert('Error loading directory: ' + data.error);
        }
    } catch (error) {
        console.error('Error loading directory:', error);
        alert('Error loading directory: ' + error.message);
    }
}

// Update breadcrumb navigation
function updateBreadcrumb(data) {
    const breadcrumb = document.getElementById('directory-breadcrumb');
    breadcrumb.innerHTML = '';

    // Add root item
    const rootItem = document.createElement('li');
    rootItem.className = 'breadcrumb-item';
    const rootLink = document.createElement('a');
    rootLink.href = '#';
    rootLink.dataset.path = '.';
    rootLink.textContent = 'Root';
    rootItem.appendChild(rootLink);
    breadcrumb.appendChild(rootItem);

    // If we're not at the root, add path segments
    if (data.relativePath !== '.') {
        const pathSegments = data.relativePath.split('/');
        let currentPath = '';

        pathSegments.forEach((segment, index) => {
            currentPath += (index === 0 ? '' : '/') + segment;

            const item = document.createElement('li');
            item.className = 'breadcrumb-item';

            if (index === pathSegments.length - 1) {
                // Last segment is active
                item.classList.add('active');
                item.textContent = segment;
            } else {
                const link = document.createElement('a');
                link.href = '#';
                link.dataset.path = currentPath;
                link.textContent = segment;
                item.appendChild(link);
            }

            breadcrumb.appendChild(item);
        });
    }
}

// Update directory list
function updateDirectoryList(data) {
    const directoryList = document.getElementById('directory-list');
    directoryList.innerHTML = '';

    // Add parent directory if not at root
    if (data.relativePath !== '.') {
        const parentItem = document.createElement('a');
        parentItem.href = '#';
        parentItem.className = 'list-group-item list-group-item-action d-flex align-items-center';
        parentItem.dataset.path = data.parent.path;
        parentItem.dataset.type = 'directory';
        parentItem.innerHTML = `
            <i class="bi bi-arrow-up-circle me-2"></i>
            <span>..</span>
        `;
        directoryList.appendChild(parentItem);
    }

    // Add directories
    data.directories.forEach(dir => {
        const dirItem = document.createElement('a');
        dirItem.href = '#';
        dirItem.className = 'list-group-item list-group-item-action d-flex align-items-center';
        dirItem.dataset.path = dir.path;
        dirItem.dataset.type = 'directory';
        dirItem.innerHTML = `
            <i class="bi bi-folder me-2"></i>
            <span>${dir.name}</span>
        `;
        directoryList.appendChild(dirItem);
    });

    // Add files (docx and image files)
    data.files.forEach(file => {
        const fileItem = document.createElement('a');
        fileItem.href = '#';
        fileItem.className = 'list-group-item list-group-item-action d-flex align-items-center text-muted';
        fileItem.dataset.path = file.path;
        fileItem.dataset.type = 'file';

        // Choose icon based on file extension
        let icon = 'bi-file-earmark';
        if (file.extension === '.docx') {
            icon = 'bi-file-earmark-word';
        } else if (file.extension === '.png' || file.extension === '.jpg' || file.extension === '.jpeg') {
            icon = 'bi-file-earmark-image';
            // Add a class to highlight image files
            if (file.extension === '.png') {
                fileItem.classList.add('text-primary');
            } else {
                fileItem.classList.add('text-success');
            }
        }

        fileItem.innerHTML = `
            <i class="bi ${icon} me-2"></i>
            <span>${file.name}</span>
        `;
        directoryList.appendChild(fileItem);
    });
}

// Handle breadcrumb click
function handleBreadcrumbClick(event) {
    event.preventDefault();

    const target = event.target;
    if (target.tagName === 'A' && target.dataset.path) {
        loadDirectoryContents(target.dataset.path);
    }
}

// Handle directory list click
function handleDirectoryListClick(event) {
    event.preventDefault();

    const target = event.target.closest('a');
    if (target && target.dataset.path && target.dataset.type === 'directory') {
        loadDirectoryContents(target.dataset.path);
    }
}

// Select the current directory
function selectCurrentDirectory() {
    document.getElementById('rename-directory').value = currentDirectoryPath;
    directoryBrowserModal.hide();
}

// Run rename tool
async function runRename() {
    // Get the directory path from the input field
    const directoryPath = document.getElementById('rename-directory').value;
    const normalizeQuotes = document.getElementById('normalize-quotes').checked;

    if (!directoryPath) {
        alert('Please enter a directory path or use the Browse button to select a directory');
        return;
    }

    try {
        const response = await fetch('/run-rename', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                directoryPath,
                normalizeQuotes 
            })
        });

        const result = await response.json();

        if (result.success) {
            // Create a modal to display the results
            const resultsHtml = `
                <div class="modal fade" id="renameResultsModal" tabindex="-1" aria-labelledby="renameResultsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="renameResultsModalLabel">Rename Tool Results</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <pre class="bg-light p-2">${result.output.stdout || 'No output'}</pre>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add the modal to the document if it doesn't exist
            if (!document.getElementById('renameResultsModal')) {
                const modalDiv = document.createElement('div');
                modalDiv.innerHTML = resultsHtml;
                document.body.appendChild(modalDiv.firstElementChild);
            } else {
                // Update the existing modal
                document.querySelector('#renameResultsModal .modal-body').innerHTML = `
                    <pre class="bg-light p-2">${result.output.stdout || 'No output'}</pre>
                `;
            }

            // Show the modal
            const renameResultsModal = new bootstrap.Modal(document.getElementById('renameResultsModal'));
            renameResultsModal.show();

            // If the directory browser was used, refresh it after renaming
            if (currentDirectoryPath === directoryPath) {
                await loadDirectoryContents(currentDirectoryPath);
            }
        } else {
            console.error('Server error:', result);
            alert('Error running rename tool: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error running rename tool:', error);
        alert('Error running rename tool: ' + (error.message || 'Unknown error'));
    }
}

// Save Firebase configuration
async function saveFirebaseConfig() {
    const useEmulator = document.getElementById('useEmulator').checked;

    try {
        const response = await fetch('/save-firebase-config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ useEmulator })
        });

        const result = await response.json();

        if (result.success) {
            alert('Firebase configuration saved successfully!');
        } else {
            console.error('Server error:', result);
            alert('Error saving Firebase configuration: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error saving Firebase configuration:', error);
        alert('Error saving Firebase configuration: ' + (error.message || 'Unknown error'));
    }
}

// Fetch category by name
async function fetchCategory() {
    const categoryName = document.getElementById('category-name').value;
    const resultsMessageElement = document.getElementById('category-results-message');
    const resultsListElement = document.getElementById('category-results-list');

    if (!categoryName) {
        alert('Please enter a category name');
        return;
    }

    // Show loading indicator
    resultsMessageElement.textContent = 'Loading...';
    resultsListElement.style.display = 'none';

    try {
        const response = await fetch('/fetch-category', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ categoryName })
        });

        const result = await response.json();

        if (result.success) {
            // Format the output for better readability
            if (result.output.stdout) {
                try {
                    // Try to parse the JSON output
                    const jsonOutput = JSON.parse(result.output.stdout);
                    if (Array.isArray(jsonOutput) && jsonOutput.length > 0) {
                        // Clear previous results
                        resultsListElement.innerHTML = '';

                        // Create a list item for each category
                        jsonOutput.forEach(category => {
                            const listItem = document.createElement('button');
                            listItem.className = 'list-group-item list-group-item-action';
                            listItem.innerHTML = `
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">${category.name}</h5>
                                    <small>ID: ${category.id}</small>
                                </div>
                                <p class="mb-1">
                                    <strong>Location:</strong> ${category.location || 'N/A'} |
                                    <strong>Field:</strong> ${category.field || 'N/A'} |
                                    <strong>Study Level:</strong> ${category.studyLevel || 'N/A'}
                                </p>
                                <small>
                                    <strong>Created:</strong> ${category.createdAt ? new Date(category.createdAt._seconds * 1000).toLocaleString() : 'N/A'}
                                </small>
                            `;

                            // Add click event to select this category for deletion
                            listItem.addEventListener('click', () => {
                                // Set the category ID in the input field
                                document.getElementById('category-id').value = category.id;

                                // Set the location dropdown if available
                                if (category.location) {
                                    const locationDropdown = document.getElementById('category-location');
                                    const options = Array.from(locationDropdown.options);
                                    const matchingOption = options.find(option => option.value === category.location);
                                    if (matchingOption) {
                                        locationDropdown.value = category.location;
                                    }
                                }

                                // Enable the delete button
                                document.getElementById('delete-category').disabled = false;

                                // Highlight the selected item
                                document.querySelectorAll('.list-group-item').forEach(item => {
                                    item.classList.remove('active');
                                });
                                listItem.classList.add('active');
                            });

                            resultsListElement.appendChild(listItem);
                        });

                        // Show the results list
                        resultsMessageElement.textContent = `Found ${jsonOutput.length} categories matching "${categoryName}". Click on a category to select it for deletion.`;
                        resultsListElement.style.display = 'block';
                    } else {
                        resultsMessageElement.textContent = 'No categories found with name: ' + categoryName;
                        resultsListElement.style.display = 'none';
                    }
                } catch (e) {
                    // If parsing fails, just show the raw output
                    resultsMessageElement.textContent = result.output.stdout || 'No categories found';
                    resultsListElement.style.display = 'none';
                }
            } else {
                resultsMessageElement.textContent = 'No categories found with name: ' + categoryName;
                resultsListElement.style.display = 'none';
            }
        } else {
            console.error('Server error:', result);
            resultsMessageElement.textContent = 'Error: ' + (result.error || 'Unknown error');
            resultsListElement.style.display = 'none';
            alert('Error fetching category: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error fetching category:', error);
        resultsMessageElement.textContent = 'Error: ' + (error.message || 'Unknown error');
        resultsListElement.style.display = 'none';
        alert('Error fetching category: ' + (error.message || 'Unknown error'));
    }
}

// Delete category by ID
async function deleteCategory() {
    const categoryId = document.getElementById('category-id').value;
    const location = document.getElementById('category-location').value;
    const deleteResultElement = document.getElementById('delete-result');
    const deleteMessageElement = document.getElementById('delete-message');

    if (!categoryId) {
        alert('Please select a category from the search results first');
        return;
    }

    if (!location) {
        alert('Please select a location for deletion');
        return;
    }

    if (!confirm(`Are you sure you want to delete the category with ID "${categoryId}"? This action cannot be undone.`)) {
        return;
    }

    // Hide previous results and show loading
    deleteResultElement.style.display = 'none';
    document.getElementById('category-results-message').textContent = 'Deleting category...';

    try {
        const response = await fetch('/delete-category', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ categoryId, location })
        });

        const result = await response.json();

        if (result.success) {
            // Show success message
            deleteMessageElement.textContent = result.message || 'Category deleted successfully';
            deleteResultElement.querySelector('.alert').className = 'alert alert-success';
            deleteResultElement.style.display = 'block';

            // Clear the category ID field and disable delete button
            document.getElementById('category-id').value = '';
            document.getElementById('delete-category').disabled = true;

            // Refresh the search results if the same search term is still there
            const categoryName = document.getElementById('category-name').value;
            if (categoryName) {
                await fetchCategory();
            } else {
                // Clear the results if no search term
                document.getElementById('category-results-message').textContent = 'Category deleted successfully. Enter a new search term to find categories.';
                document.getElementById('category-results-list').style.display = 'none';
            }
        } else {
            console.error('Server error:', result);
            deleteMessageElement.textContent = result.error || 'Error deleting category';
            deleteResultElement.querySelector('.alert').className = 'alert alert-danger';
            deleteResultElement.style.display = 'block';
        }
    } catch (error) {
        console.error('Error deleting category:', error);
        deleteMessageElement.textContent = error.message || 'Error deleting category';
        deleteResultElement.querySelector('.alert').className = 'alert alert-danger';
        deleteResultElement.style.display = 'block';
    }
}

// Delete questions by module
async function deleteQuestions() {
    const moduleName = document.getElementById('module-name').value;
    const location = document.getElementById('location-name').value;

    if (!moduleName) {
        alert('Please enter a module name');
        return;
    }

    if (!confirm(`Are you sure you want to delete all questions for module "${moduleName}"? This action cannot be undone.`)) {
        return;
    }

    try {
        const response = await fetch('/delete-questions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ moduleName, location })
        });

        const result = await response.json();

        if (result.success) {
            document.getElementById('question-results').textContent = result.output.stdout || 'Questions deleted successfully';
            alert('Questions deleted successfully!');
        } else {
            console.error('Server error:', result);
            alert('Error deleting questions: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error deleting questions:', error);
        alert('Error deleting questions: ' + (error.message || 'Unknown error'));
    }
}

// Populate shared dropdowns
function populateSharedDropdowns() {
    // Populate location dropdown
    const locationSelect = document.getElementById('shared-location');
    locationSelect.innerHTML = '<option value="" disabled selected>Select Location</option>';
    availableOptions.locations.forEach(location => {
        const option = document.createElement('option');
        option.value = location;
        option.textContent = location;
        locationSelect.appendChild(option);
    });
}

// Handle shared location dropdown change
async function handleSharedLocationChange() {
    const locationSelect = document.getElementById('shared-location');
    const fieldSelect = document.getElementById('shared-field');
    const selectedLocation = locationSelect.value;

    // Clear dependent dropdowns
    fieldSelect.innerHTML = '<option value="" disabled selected>Select Field</option>';
    document.getElementById('shared-year').innerHTML = '<option value="" disabled selected>Select Year</option>';
    document.getElementById('shared-module').innerHTML = '<option value="" disabled selected>Select Module</option>';
    document.getElementById('shared-subdir').innerHTML = '<option value="" disabled selected>Select Sub Directory</option>';
    document.getElementById('image-folder-select').innerHTML = '<option value="" disabled selected>Select Folder</option>';
    document.getElementById('qcm-json-select').innerHTML = '<option value="" disabled selected>Select JSON File</option>';
    document.getElementById('image-folder').value = '';
    document.getElementById('qcm-file').value = '';
    updateDestinationPath();

    // Populate field dropdown based on selected location
    if (selectedLocation && availableOptions.fields[selectedLocation]) {
        availableOptions.fields[selectedLocation].forEach(field => {
            const option = document.createElement('option');
            option.value = field;
            option.textContent = field;
            fieldSelect.appendChild(option);
        });
    }
}

// Handle shared field dropdown change
async function handleSharedFieldChange() {
    const locationSelect = document.getElementById('shared-location');
    const fieldSelect = document.getElementById('shared-field');
    const yearSelect = document.getElementById('shared-year');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;

    // Clear dependent dropdowns
    yearSelect.innerHTML = '<option value="" disabled selected>Select Year</option>';
    document.getElementById('shared-module').innerHTML = '<option value="" disabled selected>Select Module</option>';
    document.getElementById('shared-subdir').innerHTML = '<option value="" disabled selected>Select Sub Directory</option>';
    document.getElementById('image-folder-select').innerHTML = '<option value="" disabled selected>Select Folder</option>';
    document.getElementById('qcm-json-select').innerHTML = '<option value="" disabled selected>Select JSON File</option>';
    document.getElementById('image-folder').value = '';
    document.getElementById('qcm-file').value = '';
    updateDestinationPath();

    // Populate year dropdown based on selected location and field
    if (selectedLocation && selectedField &&
        availableOptions.years[selectedLocation] &&
        availableOptions.years[selectedLocation][selectedField]) {

        availableOptions.years[selectedLocation][selectedField].forEach(year => {
            const option = document.createElement('option');
            option.value = year.toString();
            option.textContent = year.toString();
            yearSelect.appendChild(option);
        });
    }
}

// Handle shared year dropdown change
async function handleSharedYearChange() {
    const locationSelect = document.getElementById('shared-location');
    const fieldSelect = document.getElementById('shared-field');
    const yearSelect = document.getElementById('shared-year');
    const moduleSelect = document.getElementById('shared-module');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = parseInt(yearSelect.value);

    // Clear dependent dropdowns
    moduleSelect.innerHTML = '<option value="" disabled selected>Select Module</option>';
    document.getElementById('shared-subdir').innerHTML = '<option value="" disabled selected>Select Sub Directory</option>';
    document.getElementById('image-folder-select').innerHTML = '<option value="" disabled selected>Select Folder</option>';
    document.getElementById('qcm-json-select').innerHTML = '<option value="" disabled selected>Select JSON File</option>';
    document.getElementById('image-folder').value = '';
    document.getElementById('qcm-file').value = '';
    updateDestinationPath();

    // Populate module dropdown based on selected location, field, and year
    if (selectedLocation && selectedField && selectedYear &&
        availableOptions.modules[selectedLocation] &&
        availableOptions.modules[selectedLocation][selectedField] &&
        availableOptions.modules[selectedLocation][selectedField][selectedYear]) {

        availableOptions.modules[selectedLocation][selectedField][selectedYear].forEach(module => {
            const option = document.createElement('option');
            option.value = module;
            option.textContent = module;
            moduleSelect.appendChild(option);
        });
    }
}

// Handle shared module dropdown change
async function handleSharedModuleChange() {
    const locationSelect = document.getElementById('shared-location');
    const fieldSelect = document.getElementById('shared-field');
    const yearSelect = document.getElementById('shared-year');
    const moduleSelect = document.getElementById('shared-module');
    const subDirSelect = document.getElementById('shared-subdir');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = parseInt(yearSelect.value);
    const selectedModule = moduleSelect.value;

    // Clear sub directory dropdown
    subDirSelect.innerHTML = '<option value="" disabled selected>Select Sub Directory</option>';
    document.getElementById('image-folder-select').innerHTML = '<option value="" disabled selected>Select Folder</option>';
    document.getElementById('qcm-json-select').innerHTML = '<option value="" disabled selected>Select JSON File</option>';
    document.getElementById('image-folder').value = '';
    document.getElementById('qcm-file').value = '';
    updateDestinationPath();

    // Populate sub directory dropdown based on selected location, field, year, and module
    if (selectedLocation && selectedField && selectedYear && selectedModule &&
        availableOptions.subDirs[selectedLocation] &&
        availableOptions.subDirs[selectedLocation][selectedField] &&
        availableOptions.subDirs[selectedLocation][selectedField][selectedYear] &&
        availableOptions.subDirs[selectedLocation][selectedField][selectedYear][selectedModule]) {

        availableOptions.subDirs[selectedLocation][selectedField][selectedYear][selectedModule].forEach(subDir => {
            const option = document.createElement('option');
            option.value = subDir;
            option.textContent = subDir;
            subDirSelect.appendChild(option);
        });
    }
}

// Handle shared subdir dropdown change
async function handleSharedSubdirChange() {
    // Update both image folder options and QCM JSON options
    updateImageFolderOptions();
    updateQCMJsonOptions();
    updateDestinationPath();
}

// Update image folder options based on shared selectors
async function updateImageFolderOptions() {
    const locationSelect = document.getElementById('shared-location');
    const fieldSelect = document.getElementById('shared-field');
    const yearSelect = document.getElementById('shared-year');
    const moduleSelect = document.getElementById('shared-module');
    const subDirSelect = document.getElementById('shared-subdir');
    const folderSelect = document.getElementById('image-folder-select');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = parseInt(yearSelect.value);
    const selectedModule = moduleSelect.value;
    const selectedSubDir = subDirSelect.value;

    // Clear folder select dropdown
    folderSelect.innerHTML = '<option value="" disabled selected>Select Folder</option>';
    document.getElementById('image-folder').value = '';

    if (selectedLocation && selectedField && selectedYear && selectedModule && selectedSubDir) {
        // Get available folders in the selected path
        try {
            const basePath = `${selectedLocation}/${selectedField}/${selectedYear}-year/qcm/${selectedModule}/${selectedSubDir}`;
            const response = await fetch(`/get-folders?path=${encodeURIComponent(basePath)}`);
            const folders = await response.json();

            if (folders.length > 0) {
                folders.forEach(folder => {
                    const option = document.createElement('option');
                    option.value = folder;
                    option.textContent = folder;
                    folderSelect.appendChild(option);
                });
            } else {
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'No folders found';
                option.disabled = true;
                folderSelect.appendChild(option);
            }
        } catch (error) {
            console.error('Error loading folders:', error);
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'Error loading folders';
            option.disabled = true;
            folderSelect.appendChild(option);
        }
    }
}

// Handle image folder select dropdown change
function handleImageFolderSelectChange() {
    const locationSelect = document.getElementById('shared-location');
    const fieldSelect = document.getElementById('shared-field');
    const yearSelect = document.getElementById('shared-year');
    const moduleSelect = document.getElementById('shared-module');
    const subDirSelect = document.getElementById('shared-subdir');
    const folderSelect = document.getElementById('image-folder-select');
    const imageFolderInput = document.getElementById('image-folder');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = parseInt(yearSelect.value);
    const selectedModule = moduleSelect.value;
    const selectedSubDir = subDirSelect.value;
    const selectedFolder = folderSelect.value;

    if (selectedLocation && selectedField && selectedYear && selectedModule && selectedSubDir && selectedFolder) {
        const folderPath = `${selectedLocation}/${selectedField}/${selectedYear}-year/qcm/${selectedModule}/${selectedSubDir}/${selectedFolder}`;
        imageFolderInput.value = folderPath;
        updateDestinationPath();

        // Check for image types in the selected folder
        checkImageTypesInFolder();
    }
}

// Update destination path based on selected values
function updateDestinationPath() {
    const locationSelect = document.getElementById('shared-location');
    const fieldSelect = document.getElementById('shared-field');
    const yearSelect = document.getElementById('shared-year');
    const moduleSelect = document.getElementById('shared-module');
    const destinationPathInput = document.getElementById('destination-path');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = yearSelect.value;
    const selectedModule = moduleSelect.value;

    if (selectedLocation && selectedField && selectedYear && selectedModule) {
        destinationPathInput.value = `images/${selectedLocation}/${selectedField}/${selectedYear}-year/${selectedModule}`;
    } else {
        destinationPathInput.value = '';
    }
}

// Browse for image folder
async function browseImageFolder() {
    // Show the directory browser modal
    const directoryBrowserModal = new bootstrap.Modal(document.getElementById('directoryBrowserModal'));
    directoryBrowserModal.show();

    // Set up the select directory button to update the image folder input
    document.getElementById('select-directory').onclick = function() {
        const imageFolderInput = document.getElementById('image-folder');
        imageFolderInput.value = currentDirectoryPath;
        directoryBrowserModal.hide();

        // Check for image types in the selected folder
        checkImageTypesInFolder();
    };
}

// Check for image types in the selected folder
async function checkImageTypesInFolder() {
    const imageFolder = document.getElementById('image-folder').value;
    if (!imageFolder) {
        document.getElementById('detected-images-info').textContent = '';
        return;
    }

    try {
        // Create a simple fetch request to get folder contents
        const response = await fetch(`/browse-directory?path=${encodeURIComponent(imageFolder)}`);
        const data = await response.json();

        if (response.ok) {
            // Count image types
            const imageStats = {
                png: data.pngCount || 0,
                jpg: data.jpgCount || 0,
                total: data.imageCount || 0
            };

            // Update the info text
            const infoElement = document.getElementById('detected-images-info');
            if (imageStats.total > 0) {
                infoElement.textContent = `Detected ${imageStats.total} images: ${imageStats.png} PNG, ${imageStats.jpg} JPG`;

                // Auto-select the most common type if auto-detect is selected
                if (document.getElementById('image-type-auto').checked) {
                    if (imageStats.png > imageStats.jpg) {
                        infoElement.textContent += " (Will use PNG)";
                    } else if (imageStats.jpg > 0) {
                        infoElement.textContent += " (Will use JPG)";
                    } else {
                        infoElement.textContent += " (Will use PNG by default)";
                    }
                }
            } else {
                infoElement.textContent = 'No images detected in the selected folder';
            }
        }
    } catch (error) {
        console.error('Error checking image types:', error);
        document.getElementById('detected-images-info').textContent = 'Error checking image types';
    }
}

// Upload images
async function uploadImages() {
    const imageFolder = document.getElementById('image-folder').value;
    const destinationPath = document.getElementById('destination-path').value;

    // Get the selected image type
    const imageTypeRadios = document.getElementsByName('image-type');
    let selectedImageType = 'auto'; // Default to auto

    for (const radio of imageTypeRadios) {
        if (radio.checked) {
            selectedImageType = radio.value;
            break;
        }
    }

    if (!imageFolder) {
        alert('Please enter an image folder path');
        return;
    }

    if (!destinationPath) {
        alert('Please enter a destination path');
        return;
    }

    try {
        const response = await fetch('/upload-images-with-type', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ imageFolder, destinationPath, imageType: selectedImageType })
        });

        const result = await response.json();

        if (result.success) {
            document.getElementById('image-results').textContent = result.output.stdout || 'Images uploaded successfully';
            alert('Images uploaded successfully!');
        } else {
            console.error('Server error:', result);
            alert('Error uploading images: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error uploading images:', error);
        alert('Error uploading images: ' + (error.message || 'Unknown error'));
    }
}

// Update QCM JSON options based on shared selectors
async function updateQCMJsonOptions() {
    const locationSelect = document.getElementById('shared-location');
    const fieldSelect = document.getElementById('shared-field');
    const yearSelect = document.getElementById('shared-year');
    const moduleSelect = document.getElementById('shared-module');
    const subDirSelect = document.getElementById('shared-subdir');
    const jsonSelect = document.getElementById('qcm-json-select');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = parseInt(yearSelect.value);
    const selectedModule = moduleSelect.value;
    const selectedSubDir = subDirSelect.value;

    // Clear JSON select dropdown
    jsonSelect.innerHTML = '<option value="" disabled selected>Select JSON File</option>';
    document.getElementById('qcm-file').value = '';

    if (selectedLocation && selectedField && selectedYear && selectedModule && selectedSubDir) {
        // Get available JSON files in the JSON_BANQ directory
        try {
            const jsonPath = `${selectedLocation}/${selectedField}/${selectedYear}-year/qcm/${selectedModule}/JSON_${selectedSubDir}`;
            const response = await fetch(`/get-json-files?path=${encodeURIComponent(jsonPath)}`);
            const jsonFiles = await response.json();

            if (jsonFiles.length > 0) {
                jsonFiles.forEach(file => {
                    const option = document.createElement('option');
                    option.value = file;
                    option.textContent = file;
                    jsonSelect.appendChild(option);
                });
            } else {
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'No JSON files found';
                option.disabled = true;
                jsonSelect.appendChild(option);
            }
        } catch (error) {
            console.error('Error loading JSON files:', error);
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'Error loading JSON files';
            option.disabled = true;
            jsonSelect.appendChild(option);
        }
    }
}

// Handle QCM JSON select dropdown change
function handleQCMJsonSelectChange() {
    const locationSelect = document.getElementById('shared-location');
    const fieldSelect = document.getElementById('shared-field');
    const yearSelect = document.getElementById('shared-year');
    const moduleSelect = document.getElementById('shared-module');
    const subDirSelect = document.getElementById('shared-subdir');
    const jsonSelect = document.getElementById('qcm-json-select');
    const qcmFileInput = document.getElementById('qcm-file');
    const selectedLocation = locationSelect.value;
    const selectedField = fieldSelect.value;
    const selectedYear = parseInt(yearSelect.value);
    const selectedModule = moduleSelect.value;
    const selectedSubDir = subDirSelect.value;
    const selectedJson = jsonSelect.value;

    if (selectedLocation && selectedField && selectedYear && selectedModule && selectedSubDir && selectedJson) {
        const jsonPath = `${selectedLocation}/${selectedField}/${selectedYear}-year/qcm/${selectedModule}/JSON_${selectedSubDir}/${selectedJson}`;
        qcmFileInput.value = jsonPath;
    }
}

// Process QCM
async function processQCM() {
    const qcmFile = document.getElementById('qcm-file').value;
    const imageLinksFile = document.getElementById('image-links-file').value;

    if (!qcmFile) {
        alert('Please enter a QCM file path');
        return;
    }

    if (!imageLinksFile) {
        alert('Please enter an image links file path');
        return;
    }

    try {
        const response = await fetch('/process-qcm', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ qcmFile, imageLinksFile })
        });

        const result = await response.json();

        if (result.success) {
            document.getElementById('qcm-results').textContent = result.output.stdout || 'QCM processed successfully';
            alert('QCM processed successfully!');
        } else {
            console.error('Server error:', result);
            alert('Error processing QCM: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error processing QCM:', error);
        alert('Error processing QCM: ' + (error.message || 'Unknown error'));
    }
}
